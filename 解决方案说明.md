# 阿里云通义千问模型生成细纲问题解决方案

## 问题描述

在使用阿里云通义千问模型（qwen3-235b-a22b）生成小说细纲时，会出现以下错误：

```
OpenAIClientException({
  "url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
  "method": "POST",
  "code": 500,
  "message": "Unsuccessful response",
  "body": {
    "error": [
      "message": "An system error has occurred, please try again later.",
      "type": "system_error",
      "param": null,
      "code": "system_error"
    ],
    "request_id": "4252dfe3-9a5c-9f3f-a69a-5dfa4b1b0a4f"
  }
})
```

但是在生成大纲时却没有问题。

## 原因分析

通过代码分析，我们发现阿里云通义千问模型只支持流式输出模式（stream=true），但是在生成细纲时，代码没有正确地强制使用流式模式。

## 解决方案

1. 创建专门的`AliyunQwenAdapter`类，继承自`ChatOpenAI`，强制使用流式输出：
   - 在构造函数中设置必要的headers和queryParams
   - 重写`invoke`和`stream`方法，确保始终使用流式输出

2. 修改`ModelAdapter.createLLMFromConfig`方法：
   - 检测阿里云通义千问模型
   - 使用`AliyunQwenAdapter`替代标准的`ChatOpenAI`

3. 修改`DetailedOutlineChain`和`NovelGenerationChain`类：
   - 无论使用什么模型，都使用流式模式
   - 移除条件判断，直接使用流式API

## 技术细节

1. `AliyunQwenAdapter`类：
   - 添加特殊的HTTP头：`'X-Stream-Mode': 'true'`, `'X-Use-Stream': 'true'`, `'X-Stream': 'true'`
   - 添加查询参数：`'stream': 'true'`
   - 在`invoke`方法中使用`stream`方法，然后收集所有结果

2. 流式处理：
   - 使用`StringBuffer`收集流式输出的内容
   - 确保正确处理流式响应中的每个块

## 预期效果

这些修改应该能解决阿里云通义千问模型在生成细纲时出现的问题，使其能够正常工作。由于我们现在对所有模型都使用流式模式，这也应该提高整体的稳定性和一致性。
