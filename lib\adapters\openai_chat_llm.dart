import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:langchain/langchain.dart';

/// OpenAI兼容的聊天LLM适配器
class OpenAIChatLLM extends ChatLLM {
  final String apiKey;
  final String baseUrl;
  final String model;
  final double temperature;
  final int? maxTokens;
  final double topP;
  final double repetitionPenalty;
  final http.Client? httpClient;
  final Map<String, String> extraHeaders;
  final Map<String, dynamic> extraParams;

  OpenAIChatLLM({
    required this.apiKey,
    required this.baseUrl,
    required this.model,
    this.temperature = 0.7,
    this.maxTokens,
    this.topP = 1.0,
    this.repetitionPenalty = 1.0,
    this.httpClient,
    this.extraHeaders = const {},
    this.extraParams = const {},
  });

  @override
  Future<ChatResult> invoke(ChatPromptValue input) async {
    final client = httpClient ?? http.Client();
    try {
      final messages = input.messages.map((message) {
        String role;
        if (message is HumanChatMessage) {
          role = 'user';
        } else if (message is AIChatMessage) {
          role = 'assistant';
        } else if (message is SystemChatMessage) {
          role = 'system';
        } else {
          role = 'user'; // 默认为用户消息
        }
        return {
          'role': role,
          'content': message.contentAsString,
        };
      }).toList();

      final requestBody = {
        'model': model,
        'messages': messages,
        'temperature': temperature,
        'top_p': topP,
        'frequency_penalty': repetitionPenalty,
        'stream': extraParams['stream'] ?? false, // 支持流式模式
      };

      // 如果设置了最大令牌数，添加到请求中
      if (maxTokens != null && maxTokens! > 0) {
        requestBody['max_tokens'] = maxTokens;
      }

      // 添加额外的参数
      extraParams.forEach((key, value) {
        if (key != 'stream') { // 避免重复添加stream参数
          requestBody[key] = value;
        }
      });

      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      };

      // 添加额外的头信息
      extraHeaders.forEach((key, value) {
        headers[key] = value;
      });

      final response = await client.post(
        Uri.parse('$baseUrl/chat/completions'),
        headers: headers,
        body: jsonEncode(requestBody),
      );

      if (response.statusCode != 200) {
        throw Exception(
            'API调用失败: ${response.statusCode} - ${response.body}');
      }

      final responseData = jsonDecode(utf8.decode(response.bodyBytes));
      String responseText = '';

      if (responseData['choices'] != null &&
          responseData['choices'].isNotEmpty) {
        if (responseData['choices'][0]['message'] != null) {
          responseText = responseData['choices'][0]['message']['content'] ?? '';
        }
      }

      return ChatResult(
        generations: [
          ChatGeneration(
            AIChatMessage(content: responseText),
          ),
        ],
      );
    } finally {
      if (httpClient == null) {
        client.close();
      }
    }
  }

  @override
  String get type => 'openai-chat';
}

/// 聊天LLM抽象类
abstract class ChatLLM {
  Future<ChatResult> invoke(ChatPromptValue input);
  String get type;
}
