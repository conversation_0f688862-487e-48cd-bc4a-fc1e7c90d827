from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional
from datetime import datetime

# 用户相关模式
class UserBase(BaseModel):
    username: str
    email: EmailStr

class UserCreate(UserBase):
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    is_vip: Optional[bool] = None
    vip_expire_time: Optional[datetime] = None

class UserResponse(UserBase):
    id: int
    is_vip: bool
    is_admin: bool
    vip_expire_time: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

# 令牌相关模式
class Token(BaseModel):
    access_token: str
    token_type: str
    user: UserResponse

class TokenData(BaseModel):
    username: Optional[str] = None
    user_id: Optional[int] = None

# 小说相关模式
class ChapterBase(BaseModel):
    number: int
    title: str
    content: str

class ChapterCreate(ChapterBase):
    pass

class ChapterResponse(ChapterBase):
    id: str
    created_at: datetime

    class Config:
        orm_mode = True

class NovelBase(BaseModel):
    title: str
    genre: str
    outline: Optional[str] = None
    content: Optional[str] = None
    style: Optional[str] = None
    session_id: Optional[str] = None

class NovelCreate(NovelBase):
    pass

class NovelUpdate(BaseModel):
    title: Optional[str] = None
    genre: Optional[str] = None
    outline: Optional[str] = None
    content: Optional[str] = None
    style: Optional[str] = None
    session_id: Optional[str] = None

class NovelResponse(NovelBase):
    id: str
    created_at: datetime
    updated_at: datetime
    user_id: Optional[int] = None
    chapters: List[ChapterResponse] = []

    class Config:
        orm_mode = True

# 公告相关模式
class AnnouncementBase(BaseModel):
    title: str
    content: str
    is_important: bool = False
    is_active: bool = True

class AnnouncementCreate(AnnouncementBase):
    pass

class AnnouncementUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    is_important: Optional[bool] = None
    is_active: Optional[bool] = None

class AnnouncementResponse(AnnouncementBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True
