import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/models/export_platform.dart';
import 'package:intl/intl.dart';
// 条件导入，在Web平台和非Web平台使用不同的实现
import 'export_service_web.dart' if (dart.library.io) 'export_service_io.dart';

class ExportService {
  static const Map<String, String> supportedFormats = {
    'txt': '文本文件 (*.txt)',
    'pdf': 'PDF文档 (*.pdf)',
    'html': '网页文件 (*.html)',
  };

  final ExportPlatform _platform;

  ExportService() : _platform = platform;

  Future<String> exportNovel(Novel novel, String format,
      {List<Chapter>? selectedChapters}) async {
    try {
      final chapters = selectedChapters ?? novel.chapters;
      if (chapters.isEmpty) {
        return '没有可导出的章节';
      }

      // 生成内容
      final content = _generateContent(novel, chapters, format);

      // 使用平台特定的导出方法
      if (format == 'pdf' && kIsWeb) {
        return '网页版暂不支持PDF导出';
      }

      return await _platform.exportContent(content, format, novel.title);
    } catch (e) {
      return '导出失败：$e';
    }
  }

  String _generateContent(Novel novel, List<Chapter> chapters, String format) {
    final buffer = StringBuffer();

    // 添加标题和元信息
    buffer.writeln('《${novel.title}》\n');
    buffer.writeln('作者：AI创作');
    buffer.writeln(
        '创建时间：${DateFormat('yyyy-MM-dd HH:mm:ss').format(novel.createdAt)}\n');
    buffer.writeln('=' * 50 + '\n');

    // 添加目录
    buffer.writeln('目录\n');
    for (final chapter in chapters) {
      buffer.writeln('第${chapter.number}章：${chapter.title}');
    }
    buffer.writeln('\n${'=' * 50}\n');

    // 添加章节内容
    for (final chapter in chapters) {
      buffer.writeln('\n第${chapter.number}章：${chapter.title}\n');
      buffer.writeln('-' * 30 + '\n');
      buffer.writeln(chapter.content);
      buffer.writeln('\n${'=' * 50}\n');
    }

    return buffer.toString();
  }

  // 注意：特定平台的导出实现已移至平台特定的文件中
  // export_service_io.dart 处理非Web平台
  // export_service_web.dart 处理Web平台
}
