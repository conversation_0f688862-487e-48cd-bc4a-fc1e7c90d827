import 'package:hive/hive.dart';

part 'chat_session.g.dart';

/// 聊天会话类型
enum ChatSessionType {
  normal, // 普通聊天
  novel, // 小说对话
}

/// 聊天会话模型
@HiveType(typeId: 6)
class ChatSession {
  @HiveField(0)
  final String id; // 会话ID

  @HiveField(1)
  final String title; // 会话标题

  @HiveField(2)
  final DateTime createdAt; // 创建时间

  @HiveField(3)
  DateTime lastUpdatedAt; // 最后更新时间

  @HiveField(4)
  final ChatSessionType type; // 会话类型

  @HiveField(5)
  String? novelTitle; // 关联的小说标题（仅小说对话类型）

  @HiveField(6)
  String? summary; // 会话摘要

  ChatSession({
    required this.id,
    required this.title,
    required this.createdAt,
    required this.lastUpdatedAt,
    required this.type,
    this.novelTitle,
    this.summary,
  });

  /// 创建普通聊天会话
  static ChatSession createNormal({
    required String title,
    String? summary,
  }) {
    final now = DateTime.now();
    return ChatSession(
      id: 'chat_${now.millisecondsSinceEpoch}',
      title: title,
      createdAt: now,
      lastUpdatedAt: now,
      type: ChatSessionType.normal,
      summary: summary ?? '新的聊天',
    );
  }

  /// 创建小说对话会话
  static ChatSession createNovel({
    required String novelTitle,
    String? summary,
  }) {
    final now = DateTime.now();
    return ChatSession(
      id: 'novel_chat_${now.millisecondsSinceEpoch}',
      title: '《$novelTitle》对话',
      createdAt: now,
      lastUpdatedAt: now,
      type: ChatSessionType.novel,
      novelTitle: novelTitle,
      summary: summary ?? '关于《$novelTitle》的对话',
    );
  }

  /// 更新最后更新时间
  void updateLastUpdatedAt() {
    lastUpdatedAt = DateTime.now();
  }

  /// 更新会话摘要
  void updateSummary(String newSummary) {
    summary = newSummary;
    updateLastUpdatedAt();
  }
}
