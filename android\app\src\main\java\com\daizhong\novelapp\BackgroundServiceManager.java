package com.daizhong.novelapp;

import android.app.Activity;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;
import androidx.core.app.NotificationCompat;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

/**
 * 后台服务管理器
 * 处理应用后台运行相关功能
 */
public class BackgroundServiceManager {
    private static final String TAG = "BackgroundServiceManager";
    private static final int NOTIFICATION_ID = 1;
    private static final String CHANNEL_ID = "com.daizhong.novelapp.background";
    
    private final Activity activity;
    private boolean isServiceRunning = false;

    public BackgroundServiceManager(Activity activity) {
        this.activity = activity;
        createNotificationChannel();
    }

    /**
     * 处理来自Flutter的方法调用
     */
    public void handleMethodCall(MethodCall call, MethodChannel.Result result) {
        switch (call.method) {
            case "startBackgroundService":
                String title = call.argument("title");
                String content = call.argument("content");
                startBackgroundService(title, content, result);
                break;
            case "stopBackgroundService":
                stopBackgroundService(result);
                break;
            case "isServiceRunning":
                result.success(isServiceRunning);
                break;
            default:
                result.notImplemented();
                break;
        }
    }

    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "岱宗文脉后台服务",
                    NotificationManager.IMPORTANCE_LOW);
            channel.setDescription("保持岱宗文脉在后台运行");
            
            NotificationManager notificationManager = activity.getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    /**
     * 启动后台服务
     */
    private void startBackgroundService(String title, String content, MethodChannel.Result result) {
        if (isServiceRunning) {
            result.success(true);
            return;
        }

        try {
            // 创建通知
            NotificationCompat.Builder builder = new NotificationCompat.Builder(activity, CHANNEL_ID)
                    .setSmallIcon(R.drawable.launch_logo)
                    .setContentTitle(title != null ? title : "岱宗文脉正在运行")
                    .setContentText(content != null ? content : "点击返回应用")
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .setOngoing(true);

            // 创建点击通知返回应用的Intent
            Intent notificationIntent = new Intent(activity, MainActivity.class);
            PendingIntent pendingIntent = PendingIntent.getActivity(
                    activity, 0, notificationIntent,
                    PendingIntent.FLAG_IMMUTABLE);
            builder.setContentIntent(pendingIntent);

            // 启动前台服务
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                activity.startForegroundService(new Intent(activity, BackgroundService.class));
            } else {
                activity.startService(new Intent(activity, BackgroundService.class));
            }

            isServiceRunning = true;
            result.success(true);
        } catch (Exception e) {
            Log.e(TAG, "Error starting background service", e);
            result.error("SERVICE_ERROR", "Failed to start background service", e.getMessage());
        }
    }

    /**
     * 停止后台服务
     */
    private void stopBackgroundService(MethodChannel.Result result) {
        if (!isServiceRunning) {
            result.success(true);
            return;
        }

        try {
            activity.stopService(new Intent(activity, BackgroundService.class));
            isServiceRunning = false;
            result.success(true);
        } catch (Exception e) {
            Log.e(TAG, "Error stopping background service", e);
            result.error("SERVICE_ERROR", "Failed to stop background service", e.getMessage());
        }
    }
}
