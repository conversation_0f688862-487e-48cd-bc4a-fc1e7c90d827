import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';
// import 'package:wakelock_plus/wakelock_plus.dart'; // 暂时禁用，构建时有问题
import 'package:novel_app/services/ai_service.dart';
import 'package:novel_app/services/background_service.dart';

/// 应用生命周期服务，处理应用进入后台和恢复前台时的行为
class AppLifecycleService extends GetxService with WidgetsBindingObserver {
  // 应用当前是否在后台
  final RxBool isInBackground = false.obs;

  // 保持连接的Socket
  Socket? _keepAliveSocket;

  // 后台服务
  BackgroundService? _backgroundService;

  @override
  void onInit() {
    super.onInit();
    // 注册生命周期观察者
    WidgetsBinding.instance.addObserver(this);
    print('AppLifecycleService 已初始化');

    // 延迟获取后台服务，确保它已经被初始化
    Future.delayed(const Duration(milliseconds: 500), () {
      try {
        _backgroundService = Get.find<BackgroundService>();
        print('已连接到后台服务');
      } catch (e) {
        print('无法获取后台服务: $e');
        // 尝试初始化后台服务
        _backgroundService = Get.put(BackgroundService());
      }
    });
  }

  @override
  void onClose() {
    // 取消注册生命周期观察者
    WidgetsBinding.instance.removeObserver(this);
    // 清理资源
    _cleanupBackgroundResources();
    super.onClose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    print('应用生命周期状态变化: $state');

    switch (state) {
      case AppLifecycleState.resumed:
        // 应用恢复到前台
        _handleAppResumed();
        break;
      case AppLifecycleState.inactive:
        // 应用处于非活动状态（如接听电话）
        break;
      case AppLifecycleState.paused:
        // 应用进入后台
        _handleAppPaused();
        break;
      case AppLifecycleState.detached:
        // 应用被终止
        _cleanupBackgroundResources();
        break;
      default:
        break;
    }
  }

  /// 处理应用恢复到前台
  void _handleAppResumed() {
    print('应用恢复到前台');
    isInBackground.value = false;

    // 清理后台资源
    _cleanupBackgroundResources();

    // 只在Android平台处理后台服务
    if (!kIsWeb && Platform.isAndroid) {
      // 停止后台服务（如果没有活跃的生成任务）
      if (!_checkActiveGeneration()) {
        _stopBackgroundService();
      }

      // 暂时禁用屏幕常亮功能
      print('屏幕常亮功能已禁用');
    }
  }

  /// 停止后台服务
  void _stopBackgroundService() {
    if (_backgroundService != null) {
      _backgroundService!.stopService().then((success) {
        print('后台服务停止${success ? "成功" : "失败"}');
      });
    } else {
      print('后台服务未初始化，无法停止');
    }
  }

  /// 处理应用进入后台
  void _handleAppPaused() {
    print('应用进入后台');
    isInBackground.value = true;

    // 只在Android平台处理后台服务
    if (!kIsWeb && Platform.isAndroid) {
      // 检查是否有活跃的生成任务
      bool hasActiveGeneration = _checkActiveGeneration();

      // 如果有活跃的生成任务，保持网络连接活跃
      if (hasActiveGeneration) {
        print('检测到活跃的生成任务，保持网络连接');

        // 启动后台服务
        _startBackgroundService();

        // 保持网络连接活跃
        _keepConnectionAlive();

        // 暂时禁用屏幕常亮功能
        print('屏幕常亮功能已禁用');
      }
    }
  }

  /// 启动后台服务
  void _startBackgroundService() {
    if (_backgroundService != null) {
      _backgroundService!.startService().then((success) {
        print('后台服务启动${success ? "成功" : "失败"}');
      });
    } else {
      print('后台服务未初始化，无法启动');
    }
  }

  /// 检查是否有活跃的生成任务
  bool _checkActiveGeneration() {
    try {
      // 尝试获取AIService实例
      final aiService = Get.find<AIService>();
      return aiService.hasActiveGeneration.value;
    } catch (e) {
      print('无法获取AIService: $e');
      return false;
    }
  }

  /// 保持网络连接活跃
  void _keepConnectionAlive() {
    // 只在Android平台处理
    if (kIsWeb || !Platform.isAndroid) return;

    // Android平台处理
    try {
      _setupAndroidKeepAlive();
    } catch (e) {
      print('保持连接失败: $e');
    }
  }

  /// 为Android平台设置保持连接活跃的机制
  void _setupAndroidKeepAlive() {
    // 创建一个保持连接的Socket
    try {
      // 使用Google的DNS服务器作为保持连接的目标
      Socket.connect('8.8.8.8', 53, timeout: const Duration(seconds: 5))
          .then((socket) {
        _keepAliveSocket = socket;
        print('已创建保持连接的Socket');

        // 设置保持活跃选项
        socket.setOption(SocketOption.tcpNoDelay, true);

        // 监听Socket事件
        socket.listen(
          (data) {
            // 忽略任何数据
          },
          onError: (error) {
            print('Socket错误: $error');
            // 尝试重新连接
            _reconnectSocket();
          },
          onDone: () {
            print('Socket连接已关闭');
            // 尝试重新连接
            _reconnectSocket();
          },
        );
      }).catchError((e) {
        print('创建Socket失败: $e');
      });
    } catch (e) {
      print('设置保持连接机制失败: $e');
    }
  }

  /// 重新连接Socket
  void _reconnectSocket() {
    if (!isInBackground.value) return; // 如果应用已经回到前台，不需要重连

    // 清理旧的Socket
    _cleanupSocket();

    // 延迟一秒后重新连接
    Future.delayed(const Duration(seconds: 1), () {
      if (isInBackground.value) {
        _keepConnectionAlive();
      }
    });
  }

  /// 清理Socket资源
  void _cleanupSocket() {
    try {
      _keepAliveSocket?.destroy();
      _keepAliveSocket = null;
    } catch (e) {
      print('清理Socket资源失败: $e');
    }
  }

  /// 清理所有后台资源
  void _cleanupBackgroundResources() {
    _cleanupSocket();
  }
}
