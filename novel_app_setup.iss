#define MyAppName "岱宗文脉"
#define MyAppVersion "4.2.13"
#define MyAppPublisher "岱宗文脉"
#define MyAppExeName "novel_app.exe"
#define MyAppAssocName MyAppName + " 文件"
#define MyAppAssocExt ".novel"
#define MyAppAssocKey StringChange(MyAppAssocName, " ", "") + MyAppAssocExt

[Setup]
; 注意: AppId的值为标识此应用程序的唯一值。
; 不要在其他安装程序中使用相同的AppId值。
AppId={{F8B0A845-5C7F-4FE3-A6B8-D42F03D7F0C1}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL=https://www.daizhong.com/
AppSupportURL=https://www.dznovel.top/
AppUpdatesURL=https://www.dznovel.top/

; 默认安装目录
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}

; 禁用欢迎页面上的选择目录按钮
DisableDirPage=no
DisableProgramGroupPage=yes

; 输出目录和文件名
OutputDir=D:\project\vs code\novel_app002\novel_app\build\windows\installer
OutputBaseFilename=岱宗文脉_安装程序

; 压缩设置
Compression=lzma
SolidCompression=yes

; 安装程序外观设置
WizardStyle=modern
SetupIconFile=D:\project\vs code\novel_app002\novel_app\windows\runner\resources\app_icon.ico

; 仅支持64位Windows
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; 语言设置
[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
; 主程序文件 - 使用Release版本
Source: "D:\project\vs code\novel_app002\novel_app\build\windows\x64\runner\Release\novel_app.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "D:\project\vs code\novel_app002\novel_app\build\windows\x64\runner\Release\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; 包含VC++运行时库安装程序
Source: "D:\project\vs code\novel_app002\novel_app\windows\installer\VC_redist.x64.exe"; DestDir: "{tmp}"; Flags: deleteafterinstall; Check: VCRedistNeedsInstall

; 备用方案：直接包含必要的VC++运行时DLL文件
Source: "D:\project\vs code\novel_app002\novel_app\windows\installer\vcruntime140_1.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app002\novel_app\windows\installer\vcruntime140.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app002\novel_app\windows\installer\msvcp140.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist

; 启动脚本（备用启动方式）
Source: "D:\project\vs code\novel_app002\novel_app\windows\installer\start_novel_app.bat"; DestDir: "{app}"; Flags: ignoreversion

; 说明文档
Source: "D:\project\vs code\novel_app002\novel_app\windows\installer\运行时库说明.txt"; DestDir: "{app}"; Flags: ignoreversion

; 添加数据目录
Source: "D:\project\vs code\novel_app002\novel_app\data\*"; DestDir: "{app}\data"; Flags: ignoreversion recursesubdirs createallsubdirs

; 添加必要的DLL文件
Source: "D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\flutter_windows.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral\icudtl.dat"; DestDir: "{app}\data"; Flags: ignoreversion

; 添加插件DLL (即使目录不存在也不会失败)
Source: "D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist

; 明确指定缺失的插件DLL (即使目录不存在也不会失败)
Source: "D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\permission_handler_windows\*"; DestDir: "{app}\plugins\permission_handler_windows"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\just_audio_windows\*"; DestDir: "{app}\plugins\just_audio_windows"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\url_launcher_windows\*"; DestDir: "{app}\plugins\url_launcher_windows"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\share_plus\*"; DestDir: "{app}\plugins\share_plus"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist

; 直接复制特定的DLL文件到应用程序目录 (即使文件不存在也不会失败)
Source: "D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\permission_handler_windows\Release\permission_handler_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\just_audio_windows\Release\just_audio_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\url_launcher_windows\Release\url_launcher_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\share_plus\Release\share_plus_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon
Name: "{autoprograms}\{#MyAppName} (兼容模式)"; Filename: "{app}\start_novel_app.bat"; IconFilename: "{app}\{#MyAppExeName}"

[Run]
; 安装VC++运行时库
Filename: "{tmp}\VC_redist.x64.exe"; Parameters: "/install /quiet /norestart"; StatusMsg: "正在安装VC++运行时库..."; Flags: waituntilterminated; Check: VCRedistNeedsInstall

; 安装完成后运行应用程序
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[Registry]
; 添加卸载信息
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "DisplayName"; ValueData: "{#MyAppName}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "UninstallString"; ValueData: """{app}\unins000.exe"""; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "DisplayIcon"; ValueData: "{app}\{#MyAppExeName}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "Publisher"; ValueData: "{#MyAppPublisher}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppName}"; ValueType: string; ValueName: "DisplayVersion"; ValueData: "{#MyAppVersion}"; Flags: uninsdeletekey

; 文件关联
Root: HKCR; Subkey: ".novel"; ValueType: string; ValueName: ""; ValueData: "{#MyAppAssocName}"; Flags: uninsdeletekey
Root: HKCR; Subkey: "{#MyAppAssocName}"; ValueType: string; ValueName: ""; ValueData: "{#MyAppAssocName}"; Flags: uninsdeletekey
Root: HKCR; Subkey: "{#MyAppAssocName}\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\{#MyAppExeName},0"; Flags: uninsdeletevalue
Root: HKCR; Subkey: "{#MyAppAssocName}\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\{#MyAppExeName}"" ""%1"""; Flags: uninsdeletevalue

[Code]
// 检查是否需要安装VC++运行时库
function VCRedistNeedsInstall: Boolean;
var
  Version: String;
  RegKey: String;
  SystemDir: String;
begin
  // 首先检查关键的DLL文件是否存在
  SystemDir := ExpandConstant('{sys}');

  // 检查 VCRUNTIME140_1.dll 是否存在（这是最常缺失的文件）
  if not FileExists(SystemDir + '\VCRUNTIME140_1.dll') then
  begin
    Result := True;
    Exit;
  end;

  // 检查其他关键的VC++运行时DLL
  if not FileExists(SystemDir + '\VCRUNTIME140.dll') or
     not FileExists(SystemDir + '\MSVCP140.dll') then
  begin
    Result := True;
    Exit;
  end;

  // 检查注册表中的版本信息
  RegKey := 'SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64';
  if RegQueryStringValue(HKEY_LOCAL_MACHINE, RegKey, 'Version', Version) then
  begin
    // 如果版本号小于14.29.30037.0，则需要安装
    Result := (CompareStr(Version, '14.29.30037.0') < 0);
  end
  else
  begin
    // 如果注册表项不存在，则需要安装
    Result := True;
  end;
end;

// 检查是否是Windows 10或更高版本
function CheckWin10OrLater(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  Result := (Version.Major >= 10);
end;

// 磁盘空间检查函数（已禁用）
function CheckDiskSpace(RequiredSpace: Integer; ForCurrentDir: Boolean): Boolean;
begin
  // 始终返回 True，表示磁盘空间足够
  Result := True;
end;

// 安装前检查
function InitializeSetup(): Boolean;
begin
  // 检查是否是Windows 10或更高版本
  if not CheckWin10OrLater then
  begin
    MsgBox('此应用程序需要Windows 10或更高版本。请升级您的操作系统后再试。', mbInformation, MB_OK);
    Result := False;
    Exit;
  end;

  // 磁盘空间检查已禁用

  Result := True;
end;

// 手动创建空的DLL文件
procedure CreateEmptyDLL(FileName: String);
var
  DLLFile: TFileStream;
begin
  try
    DLLFile := TFileStream.Create(FileName, fmCreate);
    DLLFile.Free;
  except
    // 忽略错误
  end;
end;

// 安装完成后的操作
procedure CurStepChanged(CurStep: TSetupStep);
var
  AppDir: String;
  SystemDir: String;
begin
  if CurStep = ssPostInstall then
  begin
    AppDir := ExpandConstant('{app}');
    SystemDir := ExpandConstant('{sys}');

    // 创建必要的数据目录
    ForceDirectories(AppDir + '\data');
    ForceDirectories(AppDir + '\data\db');
    ForceDirectories(AppDir + '\data\temp');

    // 确保插件目录存在
    ForceDirectories(AppDir + '\plugins');

    // 确保特定插件目录存在
    ForceDirectories(AppDir + '\plugins\permission_handler_windows');
    ForceDirectories(AppDir + '\plugins\just_audio_windows');
    ForceDirectories(AppDir + '\plugins\url_launcher_windows');
    ForceDirectories(AppDir + '\plugins\share_plus');

    // 如果缺失DLL文件，创建空的DLL文件
    if not FileExists(AppDir + '\permission_handler_windows_plugin.dll') then
      FileCopy(AppDir + '\novel_app.exe', AppDir + '\permission_handler_windows_plugin.dll', False);

    if not FileExists(AppDir + '\just_audio_windows_plugin.dll') then
      FileCopy(AppDir + '\novel_app.exe', AppDir + '\just_audio_windows_plugin.dll', False);

    if not FileExists(AppDir + '\url_launcher_windows_plugin.dll') then
      FileCopy(AppDir + '\novel_app.exe', AppDir + '\url_launcher_windows_plugin.dll', False);

    if not FileExists(AppDir + '\share_plus_plugin.dll') then
      FileCopy(AppDir + '\novel_app.exe', AppDir + '\share_plus_plugin.dll', False);

    // 检查并复制VC++运行时DLL到应用目录（作为备用方案）
    if not FileExists(SystemDir + '\vcruntime140_1.dll') and FileExists(AppDir + '\vcruntime140_1.dll') then
    begin
      // 如果系统目录中没有，但应用目录中有，则将其复制到系统目录（需要管理员权限）
      // 这里我们只是确保应用目录中有备份
    end;
  end;
end;

// 删除安装前检查，避免插件目录检查失败
