from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional

from ..database import get_db
from ..models import User, Announcement
from ..schemas import AnnouncementCreate, AnnouncementResponse, AnnouncementUpdate
from ..auth import get_current_active_user, get_current_admin_user

router = APIRouter(
    prefix="/api/v1/announcements",
    tags=["announcements"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=AnnouncementResponse)
async def create_announcement(
    announcement: AnnouncementCreate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """创建新公告（仅管理员）"""
    db_announcement = Announcement(
        title=announcement.title,
        content=announcement.content,
        is_important=announcement.is_important,
        is_active=announcement.is_active
    )
    db.add(db_announcement)
    db.commit()
    db.refresh(db_announcement)
    return db_announcement

@router.delete("/{announcement_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_announcement(
    announcement_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """删除公告（仅管理员）"""
    db_announcement = db.query(Announcement).filter(Announcement.id == announcement_id).first()
    if db_announcement is None:
        raise HTTPException(status_code=404, detail="公告未找到")

    db.delete(db_announcement)
    db.commit()
    return None

@router.get("/", response_model=List[AnnouncementResponse])
async def read_announcements(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """获取公告列表（公开接口）"""
    query = db.query(Announcement)
    if active_only:
        query = query.filter(Announcement.is_active == True)
    announcements = query.order_by(Announcement.created_at.desc()).offset(skip).limit(limit).all()
    return announcements

@router.get("/latest", response_model=Optional[AnnouncementResponse])
async def read_latest_announcement(
    db: Session = Depends(get_db)
):
    """获取最新公告（公开接口）"""
    announcement = db.query(Announcement).filter(Announcement.is_active == True).order_by(Announcement.created_at.desc()).first()
    if announcement is None:
        return None
    return announcement

@router.get("/{announcement_id}", response_model=AnnouncementResponse)
async def read_announcement(
    announcement_id: str,
    db: Session = Depends(get_db)
):
    """获取指定公告（公开接口）"""
    announcement = db.query(Announcement).filter(Announcement.id == announcement_id).first()
    if announcement is None:
        raise HTTPException(status_code=404, detail="公告未找到")
    return announcement

@router.put("/{announcement_id}", response_model=AnnouncementResponse)
async def update_announcement(
    announcement_id: str,
    announcement_update: AnnouncementUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """更新公告（仅管理员）"""
    db_announcement = db.query(Announcement).filter(Announcement.id == announcement_id).first()
    if db_announcement is None:
        raise HTTPException(status_code=404, detail="公告未找到")

    # 更新公告信息
    for key, value in announcement_update.dict(exclude_unset=True).items():
        setattr(db_announcement, key, value)

    db.commit()
    db.refresh(db_announcement)
    return db_announcement

@router.delete("/{announcement_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_announcement(
    announcement_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """删除公告（仅管理员）"""
    db_announcement = db.query(Announcement).filter(Announcement.id == announcement_id).first()
    if db_announcement is None:
        raise HTTPException(status_code=404, detail="公告未找到")

    db.delete(db_announcement)
    db.commit()
    return {"status": "success"}
