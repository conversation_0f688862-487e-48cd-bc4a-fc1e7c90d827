import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/services/ai_service.dart';

class ChapterDetailScreen extends StatefulWidget {
  const ChapterDetailScreen({super.key});

  @override
  State<ChapterDetailScreen> createState() => _ChapterDetailScreenState();
}

class _ChapterDetailScreenState extends State<ChapterDetailScreen> {
  late Chapter chapter;
  late Novel _currentNovel;
  final _novelController = Get.find<NovelController>();
  final _aiService = Get.find<AIService>();
  TextSelection? _lastSelection;
  int _currentChapterIndex = 0;

  @override
  void initState() {
    super.initState();
    chapter = Get.arguments;
    // 获取当前小说
    _currentNovel = _novelController.novels.firstWhere(
      (novel) => novel.chapters.contains(chapter),
      orElse: () => throw Exception('找不到章节所属小说'),
    );
    // 获取章节索引
    _currentChapterIndex = _currentNovel.chapters.indexOf(chapter);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('第${chapter.number}章：${chapter.title}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: '编辑',
            onPressed: () {
              Get.toNamed('/chapter_edit', arguments: chapter);
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              '第${chapter.number}章：${chapter.title}',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SelectableText(
              chapter.content,
              style: TextStyle(
                fontSize: 16,
                height: 1.8,
                // 使用主题的onSurface颜色，确保在深色模式下有足够的对比度
                color: Theme.of(context).colorScheme.onSurface,
                // 增加字体粗细，提高可读性
                fontWeight: FontWeight.w400,
              ),
              onSelectionChanged: (selection, cause) {
                if (!selection.isCollapsed) {
                  _lastSelection = selection;
                }
              },
              contextMenuBuilder: (context, editableTextState) {
                if (editableTextState.textEditingValue.selection.isValid &&
                    !editableTextState.textEditingValue.selection.isCollapsed) {
                  final selectedText = editableTextState
                      .textEditingValue.selection
                      .textInside(chapter.content);

                  return AdaptiveTextSelectionToolbar(
                    anchors: editableTextState.contextMenuAnchors,
                    children: [
                      TextSelectionToolbarTextButton(
                        padding: const EdgeInsets.all(12.0),
                        onPressed: () {
                          editableTextState.hideToolbar();
                          _showAIEditDialog(selectedText);
                        },
                        child: const Text(
                          'AI编辑',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                      const VerticalDivider(
                        width: 1,
                        indent: 8,
                        endIndent: 8,
                      ),
                      TextSelectionToolbarTextButton(
                        padding: const EdgeInsets.all(12.0),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: selectedText));
                          editableTextState.hideToolbar();
                        },
                        child: const Text(
                          '复制',
                          style: TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  );
                }

                return AdaptiveTextSelectionToolbar.buttonItems(
                  anchors: editableTextState.contextMenuAnchors,
                  buttonItems: editableTextState.contextMenuButtonItems,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showAIEditDialog(String selectedText) async {
    final promptController = TextEditingController();
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('AI编辑文本'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('已选中文本：',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Container(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.3,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  padding: const EdgeInsets.all(8),
                  child: SingleChildScrollView(
                    child: Text(
                      selectedText,
                      style: TextStyle(
                        // 使用主题的onSurface颜色，确保在深色模式下有足够的对比度
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: promptController,
                  decoration: const InputDecoration(
                    labelText: '修改指令',
                    hintText: '例如：改写成更生动的描述',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(promptController.text),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      _modifyTextWithAI(selectedText, result);
    }
  }

  Future<void> _modifyTextWithAI(String originalText, String prompt) async {
    try {
      final modifiedTextController = TextEditingController();

      Get.dialog(
        AlertDialog(
          title: const Text('AI正在修改文本'),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const LinearProgressIndicator(),
                  const SizedBox(height: 16),
                  TextField(
                    controller: modifiedTextController,
                    maxLines: null,
                    readOnly: true,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.all(8),
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('取消'),
            ),
          ],
        ),
        barrierDismissible: false,
      );

      final systemPrompt = '''请根据用户的要求修改以下文本。要求：
1. 保持修改后的文本与上下文的连贯性
2. 保持人物、场景等设定的一致性
3. 确保修改后的文本符合整体风格
4. 避免出现与原意相违背的内容

原文：
$originalText

用户要求：
$prompt''';

      const userPrompt = '请按照上述要求修改文本，直接输出修改后的内容，不需要其他解释。';

      String modifiedText = '';
      await for (final chunk in _aiService.generateTextStream(
        systemPrompt: systemPrompt,
        userPrompt: userPrompt,
        maxTokens: 2000,
        temperature: 0.7,
      )) {
        modifiedText += chunk;
        modifiedTextController.text = modifiedText;
      }

      Get.back(); // 关闭生成对话框

      if (_lastSelection != null) {
        // 更新章节内容
        final originalContent = chapter.content;
        final start = _lastSelection!.start;
        final end = _lastSelection!.end;

        if (start >= 0 && end <= originalContent.length && start <= end) {
          final newContent =
              originalContent.replaceRange(start, end, modifiedText.trim());

          // 创建更新后的章节
          final updatedChapter = chapter.copyWith(content: newContent);

          // 更新小说对象
          final updatedChapters = List<Chapter>.from(_currentNovel.chapters);
          updatedChapters[_currentChapterIndex] = updatedChapter;
          final updatedNovel = _currentNovel.copyWith(
            chapters: updatedChapters,
            content: updatedChapters.map((c) => c.content).join('\n\n'),
          );

          // 保存到数据库
          _novelController.saveNovel(updatedNovel);

          // 更新本地状态
          setState(() {
            chapter = updatedChapter;
            _currentNovel = updatedNovel;
          });

          Get.snackbar(
            '修改完成',
            '文本已更新',
            backgroundColor: Colors.green.withAlpha(25),
            duration: const Duration(seconds: 2),
          );
        }
      }
    } catch (e) {
      Get.back(); // 关闭生成对话框
      Get.snackbar(
        '修改失败',
        e.toString(),
        backgroundColor: Colors.red.withAlpha(25),
        duration: const Duration(seconds: 3),
      );
    }
  }
}
