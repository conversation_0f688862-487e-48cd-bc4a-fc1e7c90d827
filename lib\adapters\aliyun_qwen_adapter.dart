import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';

/// 阿里云通义千问模型适配器
/// 专门处理阿里云通义千问模型的特殊需求
class AliyunQwenAdapter extends ChatOpenAI {
  final bool enableThinking;

  AliyunQwenAdapter({
    required String apiKey,
    required String baseUrl,
    required String model,
    double temperature = 0.7,
    int? maxTokens,
    double topP = 1.0,
    Map<String, String> extraHeaders = const {},
    this.enableThinking = false,
  }) : super(
          apiKey: apiKey,
          baseUrl: baseUrl,
          defaultOptions: ChatOpenAIOptions(
            model: model,
            temperature: temperature,
            maxTokens: maxTokens,
            topP: topP,
          ),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'X-Stream-Mode': 'true',
            'X-Use-Stream': 'true',
            'X-Stream': 'true',
            ...extraHeaders,
          },
          queryParams: {
            'stream': 'true',
          },
        );

  @override
  Future<LanguageModelResult<AIChatMessage>> invoke(
    PromptValue input, {
    ChatOpenAIOptions? options,
  }) async {
    // 确保input是ChatPromptValue类型
    if (input is! ChatPromptValue) {
      throw ArgumentError('Input must be a ChatPromptValue');
    }

    // 在输入中添加深度思考模式参数
    final modifiedInput = _addThinkingModeToInput(input);

    // 直接使用父类的stream方法，然后收集所有结果
    final buffer = StringBuffer();
    final streamResult = super.stream(modifiedInput, options: options);

    await for (final result in streamResult) {
      if (result.generations.isNotEmpty) {
        final generation = result.generations.first;
        final message = generation.output;
        buffer.write(message.contentAsString);
      }
    }

    // 创建一个包含完整内容的消息
    final completeMessage = AIChatMessage(content: buffer.toString());

    return LanguageModelResult(generations: [ChatGeneration(completeMessage)]);
  }

  @override
  Stream<LanguageModelResult<AIChatMessage>> stream(
    PromptValue input, {
    ChatOpenAIOptions? options,
  }) {
    // 确保input是ChatPromptValue类型
    if (input is! ChatPromptValue) {
      throw ArgumentError('Input must be a ChatPromptValue');
    }

    // 直接调用父类的stream方法，依赖构造函数中设置的headers和queryParams
    // 在请求中添加深度思考模式参数
    final modifiedInput = _addThinkingModeToInput(input);
    return super.stream(modifiedInput, options: options);
  }

  /// 在输入中添加深度思考模式参数
  PromptValue _addThinkingModeToInput(PromptValue input) {
    if (input is! ChatPromptValue) {
      return input;
    }

    // 创建一个新的消息列表，添加深度思考模式参数
    final messages = List<ChatMessage>.from(input.messages);

    // 添加一个系统消息，包含深度思考模式参数
    if (messages.isNotEmpty && messages.first is SystemChatMessage) {
      // 如果第一个消息是系统消息，修改它
      final firstMessage = messages.first as SystemChatMessage;
      final content = firstMessage.content.toString();
      final newContent =
          '$content\n\nextra_body={"enable_thinking": $enableThinking}';
      messages[0] = SystemChatMessage(content: newContent);
    } else {
      // 如果没有系统消息，添加一个
      messages.insert(
          0,
          SystemChatMessage(
              content: 'extra_body={"enable_thinking": $enableThinking}'));
    }

    return ChatPromptValue(messages);
  }
}
