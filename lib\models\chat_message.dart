import 'package:hive/hive.dart';

part 'chat_message.g.dart';

/// 对话消息类型
enum ChatMessageType {
  user, // 用户消息
  ai, // AI消息
  system // 系统消息
}

/// 对话消息模型
@HiveType(typeId: 5)
class ChatMessage {
  @HiveField(0)
  final String id; // 消息ID

  @HiveField(1)
  final String content; // 消息内容

  @HiveField(2)
  final DateTime timestamp; // 消息时间戳

  @HiveField(3)
  final ChatMessageType type; // 消息类型

  @HiveField(4)
  final String novelTitle; // 关联的小说标题

  @HiveField(5)
  final int? chapterNumber; // 关联的章节编号（可选）

  @HiveField(6)
  final String? sessionId; // 关联的会话ID（可选）

  ChatMessage({
    required this.id,
    required this.content,
    required this.timestamp,
    required this.type,
    required this.novelTitle,
    this.chapterNumber,
    this.sessionId,
  });

  /// 创建用户消息
  static ChatMessage user({
    required String content,
    required String novelTitle,
    int? chapterNumber,
    String? sessionId,
  }) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      timestamp: DateTime.now(),
      type: ChatMessageType.user,
      novelTitle: novelTitle,
      chapterNumber: chapterNumber,
      sessionId: sessionId,
    );
  }

  /// 创建AI消息
  static ChatMessage ai({
    required String content,
    required String novelTitle,
    int? chapterNumber,
    String? sessionId,
  }) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      timestamp: DateTime.now(),
      type: ChatMessageType.ai,
      novelTitle: novelTitle,
      chapterNumber: chapterNumber,
      sessionId: sessionId,
    );
  }

  /// 创建系统消息
  static ChatMessage system({
    required String content,
    required String novelTitle,
    int? chapterNumber,
    String? sessionId,
  }) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      timestamp: DateTime.now(),
      type: ChatMessageType.system,
      novelTitle: novelTitle,
      chapterNumber: chapterNumber,
      sessionId: sessionId,
    );
  }
}
