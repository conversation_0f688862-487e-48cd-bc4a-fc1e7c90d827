import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/services/chat_history_service.dart';
import 'package:novel_app/pages/chat_history_page.dart';
import 'package:novel_app/controllers/novel_controller.dart';

class ChatHistoryListScreen extends StatefulWidget {
  const ChatHistoryListScreen({Key? key}) : super(key: key);

  @override
  State<ChatHistoryListScreen> createState() => _ChatHistoryListScreenState();
}

class _ChatHistoryListScreenState extends State<ChatHistoryListScreen> {
  final ChatHistoryService _chatHistoryService = Get.find<ChatHistoryService>();
  final NovelController _novelController = Get.find<NovelController>();
  
  List<String> _novelTitles = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadNovelTitles();
  }

  Future<void> _loadNovelTitles() async {
    setState(() {
      _isLoading = true;
    });

    // 获取所有小说标题
    _novelTitles = _chatHistoryService.getAllNovelTitles();
    
    // 如果没有历史对话记录，也添加当前小说库中的小说
    if (_novelTitles.isEmpty) {
      final novels = _novelController.novels;
      for (final novel in novels) {
        if (!_novelTitles.contains(novel.title)) {
          _novelTitles.add(novel.title);
        }
      }
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('历史对话'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNovelTitles,
            tooltip: '刷新列表',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _novelTitles.isEmpty
              ? _buildEmptyState()
              : _buildNovelList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            '暂无历史对话',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '生成小说后，您可以在这里查看和继续与AI的对话',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('创建新小说'),
            onPressed: () {
              Get.back();
              Get.toNamed('/generator');
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNovelList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _novelTitles.length,
      itemBuilder: (context, index) {
        final title = _novelTitles[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: const CircleAvatar(
              child: Icon(Icons.book),
            ),
            title: Text(title),
            subtitle: const Text('点击查看对话历史'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Get.to(() => ChatHistoryPage(novelTitle: title));
            },
          ),
        );
      },
    );
  }
}
