package com.daizhong.novelapp;

import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 权限管理器
 * 处理应用权限请求和结果
 */
public class PermissionManager {
    private static final String TAG = "PermissionManager";
    private static final int REQUEST_PERMISSION_CODE = 1001;
    
    private final Activity activity;
    private MethodChannel.Result pendingResult;
    private List<String> pendingPermissions;

    public PermissionManager(Activity activity) {
        this.activity = activity;
    }

    /**
     * 处理来自Flutter的方法调用
     */
    public void handleMethodCall(MethodCall call, MethodChannel.Result result) {
        switch (call.method) {
            case "requestPermissions":
                List<String> permissions = call.argument("permissions");
                requestPermissions(permissions, result);
                break;
            case "checkPermission":
                String permission = call.argument("permission");
                checkPermission(permission, result);
                break;
            default:
                result.notImplemented();
                break;
        }
    }

    /**
     * 请求多个权限
     */
    private void requestPermissions(List<String> permissions, MethodChannel.Result result) {
        if (permissions == null || permissions.isEmpty()) {
            result.error("INVALID_ARGUMENT", "Permissions list cannot be null or empty", null);
            return;
        }

        // 检查哪些权限需要请求
        List<String> permissionsToRequest = new ArrayList<>();
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                permissionsToRequest.add(permission);
            }
        }

        // 如果所有权限都已授予，直接返回成功
        if (permissionsToRequest.isEmpty()) {
            Map<String, Boolean> resultMap = new HashMap<>();
            for (String permission : permissions) {
                resultMap.put(permission, true);
            }
            result.success(resultMap);
            return;
        }

        // 保存结果回调和待请求权限
        pendingResult = result;
        pendingPermissions = permissions;

        // 请求权限
        ActivityCompat.requestPermissions(
                activity,
                permissionsToRequest.toArray(new String[0]),
                REQUEST_PERMISSION_CODE);
    }

    /**
     * 检查单个权限
     */
    private void checkPermission(String permission, MethodChannel.Result result) {
        if (permission == null || permission.isEmpty()) {
            result.error("INVALID_ARGUMENT", "Permission cannot be null or empty", null);
            return;
        }

        boolean isGranted = ContextCompat.checkSelfPermission(activity, permission) == PackageManager.PERMISSION_GRANTED;
        result.success(isGranted);
    }

    /**
     * 处理权限请求结果
     */
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode != REQUEST_PERMISSION_CODE || pendingResult == null || pendingPermissions == null) {
            return;
        }

        // 构建结果映射
        Map<String, Boolean> resultMap = new HashMap<>();
        for (String permission : pendingPermissions) {
            boolean isGranted = ContextCompat.checkSelfPermission(activity, permission) == PackageManager.PERMISSION_GRANTED;
            resultMap.put(permission, isGranted);
        }

        // 返回结果
        pendingResult.success(resultMap);

        // 清除挂起的请求
        pendingResult = null;
        pendingPermissions = null;
    }
}
