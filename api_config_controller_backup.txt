﻿import 'dart:io';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:novel_app/services/ai_service.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:novel_app/models/embedding_model_config.dart';
import 'package:novel_app/services/embedding_service.dart';

class ModelConfig {
  String name; // 妯″瀷鍚嶇О
  String apiKey; // API瀵嗛挜
  String apiUrl; // API鍦板潃
  String apiPath; // API璺緞
  String model; // 鍏蜂綋妯″瀷鍚嶇О
  List<String> modelVariants; // 妯″瀷鍙樹綋鍒楄〃锛堝妯″瀷鏍囪瘑绗︼級
  String apiFormat; // API鏍煎紡锛堝OpenAI API鍏煎绛夛級
  String appId; // 搴旂敤ID锛堢櫨搴﹀崈甯嗙瓑闇€瑕侊級
  bool isCustom; // 鏄惁涓鸿嚜瀹氫箟妯″瀷
  double temperature;
  double topP;
  int maxTokens;
  double repetitionPenalty; // 娣诲姞閲嶅鎯╃綒鍙傛暟
  bool useProxy; // 鏄惁浣跨敤浠ｇ悊
  String proxyUrl; // 浠ｇ悊鏈嶅姟鍣ㄥ湴鍧€
  int timeout; // 璇锋眰瓒呮椂鏃堕棿锛堢锛?

  ModelConfig({
    required this.name,
    required this.apiKey,
    required this.apiUrl,
    required this.apiPath,
    required this.model,
    this.modelVariants = const [],
    required this.apiFormat,
    this.appId = '',
    this.isCustom = false,
    this.temperature = 0.7,
    this.topP = 1.0,
    this.maxTokens = 6000,
    this.repetitionPenalty = 1.3, // 璁剧疆榛樿鍊?
    this.useProxy = false, // 榛樿涓嶄娇鐢ㄤ唬鐞?
    this.proxyUrl = '', // 榛樿浠ｇ悊鍦板潃涓虹┖
    this.timeout = 60, // 榛樿瓒呮椂鏃堕棿涓?0绉?
  });

  Map<String, dynamic> toJson() => {
        'name': name,
        'apiKey': apiKey,
        'apiUrl': apiUrl,
        'apiPath': apiPath,
        'model': model,
        'modelVariants': modelVariants,
        'apiFormat': apiFormat,
        'appId': appId,
        'isCustom': isCustom,
        'temperature': temperature,
        'topP': topP,
        'maxTokens': maxTokens,
        'repetitionPenalty': repetitionPenalty, // 娣诲姞鍒?JSON
        'useProxy': useProxy, // 娣诲姞浠ｇ悊璁剧疆
        'proxyUrl': proxyUrl, // 娣诲姞浠ｇ悊URL
        'timeout': timeout, // 娣诲姞瓒呮椂鏃堕棿
      };

  factory ModelConfig.fromJson(Map<String, dynamic> json) => ModelConfig(
        name: json['name'] as String? ?? '',
        apiKey: json['apiKey'] as String? ?? '',
        apiUrl: json['apiUrl'] as String? ?? '',
        apiPath: json['apiPath'] as String? ?? '',
        model: json['model'] as String? ?? '',
        modelVariants: json['modelVariants'] != null
            ? List<String>.from(json['modelVariants'])
            : [],
        apiFormat: json['apiFormat'] as String? ?? 'OpenAI API鍏煎',
        appId: json['appId'] as String? ?? '',
        isCustom: json['isCustom'] as bool? ?? false,
        temperature: (json['temperature'] as num?)?.toDouble() ?? 0.7,
        topP: (json['topP'] as num?)?.toDouble() ?? 1.0,
        maxTokens: (json['maxTokens'] as num?)?.toInt() ?? 4000,
        repetitionPenalty:
            (json['repetitionPenalty'] as num?)?.toDouble() ?? 1.3, // 浠?JSON 璇诲彇
        useProxy: json['useProxy'] as bool? ?? false, // 浠?JSON 璇诲彇浠ｇ悊璁剧疆
        proxyUrl: json['proxyUrl'] as String? ?? '', // 浠?JSON 璇诲彇浠ｇ悊URL
        timeout: (json['timeout'] as num?)?.toInt() ?? 60, // 浠?JSON 璇诲彇瓒呮椂鏃堕棿
      );

  ModelConfig copyWith({
    String? name,
    String? apiKey,
    String? apiUrl,
    String? apiPath,
    String? model,
    List<String>? modelVariants,
    String? apiFormat,
    String? appId,
    bool? isCustom,
    double? temperature,
    double? topP,
    int? maxTokens,
    double? repetitionPenalty, // 娣诲姞鍒?copyWith
    bool? useProxy, // 娣诲姞浠ｇ悊璁剧疆
    String? proxyUrl, // 娣诲姞浠ｇ悊URL
    int? timeout, // 娣诲姞瓒呮椂鏃堕棿
  }) {
    return ModelConfig(
      name: name ?? this.name,
      apiKey: apiKey ?? this.apiKey,
      apiUrl: apiUrl ?? this.apiUrl,
      apiPath: apiPath ?? this.apiPath,
      model: model ?? this.model,
      modelVariants: modelVariants ?? this.modelVariants,
      apiFormat: apiFormat ?? this.apiFormat,
      appId: appId ?? this.appId,
      isCustom: isCustom ?? this.isCustom,
      temperature: temperature ?? this.temperature,
      topP: topP ?? this.topP,
      maxTokens: maxTokens ?? this.maxTokens,
      repetitionPenalty: repetitionPenalty ?? this.repetitionPenalty, // 娣诲姞鍒版瀯閫?
      useProxy: useProxy ?? this.useProxy, // 娣诲姞浠ｇ悊璁剧疆
      proxyUrl: proxyUrl ?? this.proxyUrl, // 娣诲姞浠ｇ悊URL
      timeout: timeout ?? this.timeout, // 娣诲姞瓒呮椂鏃堕棿
    );
  }

  // 娣诲姞妯″瀷鍙樹綋
  void addModelVariant(String variant) {
    if (!modelVariants.contains(variant) && variant.isNotEmpty) {
      modelVariants.add(variant);
    }
  }

  // 鍒犻櫎妯″瀷鍙樹綋
  void removeModelVariant(String variant) {
    modelVariants.remove(variant);
  }

  // 鍒囨崲褰撳墠妯″瀷涓烘寚瀹氬彉浣?
  void switchToVariant(String variant) {
    if (modelVariants.contains(variant)) {
      model = variant;
    }
  }
}

class ApiConfigController extends GetxController {
  static const _boxName = 'api_config';
  static const _customModelsKey = 'custom_models';
  static const _embeddingModelKey = 'embedding_model';
  late final Box<dynamic> _box;

  final RxString selectedModelId = ''.obs;
  final RxList<ModelConfig> models = <ModelConfig>[].obs;
  final RxDouble temperature = 0.7.obs;
  final RxDouble topP = 1.0.obs;
  final RxInt maxTokens = 4000.obs;
  final RxDouble repetitionPenalty = 1.3.obs; // 娣诲姞閲嶅鎯╃綒鍙傛暟鐨勫搷搴斿紡鍙橀噺

  // 宓屽叆妯″瀷閰嶇疆
  final Rx<EmbeddingModelConfig> embeddingModel = EmbeddingModelConfig(
    name: '闃块噷鐧剧偧宓屽叆妯″瀷',
    apiKey: '',
    baseUrl: 'https://dashscope.aliyuncs.com',
    apiPath: '/api/v1/embeddings',
    modelName: 'text-embedding-v3',
    apiFormat: '闃块噷鐧剧偧',
    topK: 5,
    enabled: false,
    useProxy: false,
    proxyUrl: '',
    timeout: 60,
  ).obs;

  final List<ModelConfig> _defaultModels = [
    // 娣诲姞涓浆绔欐ā鍨嬪苟璁句负绗竴涓紙榛樿锛夋ā鍨?
    ModelConfig(
      name: '涓浆绔?,
      apiKey: '', // 鐣欑┖锛岀敤鎴峰湪璁剧疆椤甸潰濉啓
      apiUrl: 'https://dzwm.xyz',
      apiPath: '/v1/text/chatcompletion_v2',
      model: 'gpt-4.1-mini-2025-04-14',
      modelVariants: [
        'gpt-4.1-mini-2025-04-14',
      ],
      apiFormat: 'OpenAI API鍏煎',
      appId: '',
      maxTokens: 6000,
    ),
    ModelConfig(
      name: 'ChatGPT',
      apiKey: '',
      apiUrl: 'https://api.openai.com',
      apiPath: '/v1/text/chatcompletion_v2',
      model: 'gpt-4',
      apiFormat: 'OpenAI API鍏煎',
    ),
    ModelConfig(
      name: '纭呭熀娴佸姩',
      apiKey: '',
