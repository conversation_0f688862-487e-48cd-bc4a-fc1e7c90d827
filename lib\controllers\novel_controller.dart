import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'package:path_provider/path_provider.dart';
import 'package:get/get.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/models/novel_outline.dart';
import 'package:novel_app/prompts/genre_prompts.dart';
import 'package:novel_app/models/character_type.dart';
import 'package:novel_app/models/character_card.dart';
import 'package:novel_app/models/generation_mode.dart';
import 'package:novel_app/services/cache_service.dart';
import 'package:novel_app/services/export_service.dart';
import 'package:novel_app/services/character_type_service.dart';
import 'package:novel_app/services/character_card_service.dart';
import 'package:novel_app/services/ai_service.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:novel_app/models/writing_style_package.dart';
import 'package:novel_app/langchain/services/novel_generation_service.dart';
import 'package:novel_app/langchain/services/lightweight_generation_service.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/langchain/models/novel_memory.dart';
import 'package:novel_app/services/novel_vectorization_service.dart';
import 'package:novel_app/services/enhanced_outline_import_service.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:flutter/material.dart';

// Add Enum at the top or in a separate file
enum GenerationStage {
  idle, // 初始状态
  generatingOutline, // 正在生成大纲
  outlineReady, // 大纲已生成，等待用户确认/修改
  generatingDetailedOutline, // 正在生成细纲
  detailedOutlineReady, // 细纲已生成，等待用户确认
  generatingChapters, // 正在生成章节
  generationComplete, // 全部完成
  error, // 发生错误
}

class NovelController extends GetxController {
  final _cacheService = Get.find<CacheService>();
  final _exportService = ExportService();
  final _characterTypeService = Get.find<CharacterTypeService>();
  final _characterCardService = Get.find<CharacterCardService>();
  final _aiService = Get.find<AIService>();

  final novels = <Novel>[].obs;
  final title = ''.obs;
  final background = ''.obs;
  final otherRequirements = ''.obs;
  final style = '轻松幽默'.obs;
  final selectedGenres = <String>[].obs;

  // 添加目标读者变量
  final targetReader = '男性向'.obs;

  // 新增角色选择相关的变量
  final selectedCharacterTypes = <CharacterType>[].obs;
  final Map<String, CharacterCard> selectedCharacterCards =
      <String, CharacterCard>{}.obs;

  // 短篇小说相关变量
  final novelType = NovelType.longNovel.obs;
  final shortNovelWordCount = ShortNovelWordCount.words10000.obs;
  final Rx<ShortNovelOutline?> currentShortNovelOutline =
      Rx<ShortNovelOutline?>(null);
  final shortNovelGeneratedContent = ''.obs;
  final shortNovelCurrentPart = 0.obs;

  final isGenerating = false.obs;
  final generationStatus = ''.obs;
  final generationProgress = 0.0.obs;
  final realtimeOutput = ''.obs;
  final isPaused = false.obs;
  final _shouldStop = false.obs;
  final _currentChapter = 0.obs; // 添加当前章节记录
  final _hasOutline = false.obs; // 添加大纲状态记录

  static const String _novelsBoxName = 'novels';
  static const String _chaptersBoxName = 'generated_chapters';
  late final Box<dynamic> _novelsBox;
  late final Box<dynamic> _chaptersBox;
  final RxList<Chapter> _generatedChapters = <Chapter>[].obs;

  List<Chapter> get generatedChapters => _generatedChapters;

  // 添加大纲相关变量
  final currentOutline = Rx<NovelOutline?>(null);
  final rawGeneratedOutlineText = ''.obs;
  final isUsingOutline = false.obs;

  // 新增属性
  final RxString _currentNovelBackground = ''.obs;
  final RxList<String> _specialRequirements = <String>[].obs;
  final RxString _selectedStyle = ''.obs;
  final RxInt _totalChapters = 5.obs;
  final RxString _currentNovelTitle = ''.obs;

  // 会话ID管理
  final Map<String, String> _novelSessionIds = <String, String>{};
  final RxString _currentSessionId = ''.obs;

  // 新增getter
  String get currentNovelBackground => _currentNovelBackground.value;
  List<String> get specialRequirements => _specialRequirements;
  String get selectedStyle => _selectedStyle.value;
  int get totalChapters => _totalChapters.value;
  RxInt get totalChaptersRx => _totalChapters;
  String get currentNovelTitle => _currentNovelTitle.value;
  String get currentSessionId => _currentSessionId.value;

  // 新增setter方法
  void setNovelTitle(String title) {
    _currentNovelTitle.value = title;
  }

  void setNovelBackground(String background) {
    _currentNovelBackground.value = background;
  }

  void setSpecialRequirements(List<String> requirements) {
    _specialRequirements.assignAll(requirements);
  }

  void setSelectedGenres(List<String> genres) {
    selectedGenres.assignAll(genres);
  }

  void setSelectedCharacterTypes(List<CharacterType> types) {
    selectedCharacterTypes.assignAll(types);
  }

  void setSelectedCharacterCards(Map<String, CharacterCard> cards) {
    selectedCharacterCards.assignAll(cards);
  }

  void setSelectedStyle(String style) {
    _selectedStyle.value = style;
  }

  void setTargetReader(String value) => targetReader.value = value;

  void setTotalChapters(int value) {
    if (value > 0) {
      // 如果用户输入的值超过1000，给出提示但仍然允许设置
      if (value > 1000) {
        Get.snackbar(
          '提示',
          '章节数量较多，生成时间可能会较长，建议不要超过1000章',
          duration: const Duration(seconds: 5),
        );
      }
      _totalChapters.value = value;
    } else {
      Get.snackbar('错误', '章节数量必须大于0');
      _totalChapters.value = 1; // 设置为最小值
    }
  }

  void setUsingOutline(bool useOutline) {
    isUsingOutline.value = useOutline;
  }

  final Rx<WritingStylePackage?> selectedWritingStyle =
      Rx<WritingStylePackage?>(null);

  void selectWritingStyle(WritingStylePackage? package) {
    selectedWritingStyle.value = package;
  }

  String getWritingStylePrompt() {
    if (selectedWritingStyle.value == null) {
      return '';
    }

    final package = selectedWritingStyle.value!;
    return '''
请参考以下文风示例进行创作：
作者：${package.author}
风格描述：${package.description}
示例文本：
${package.sampleTexts.join('\n')}

请严格按照上述文风进行创作，保持相同的写作风格、语言特点和叙事方式。
''';
  }

  // 添加LangChain服务
  final NovelGenerationService _langchainService =
      Get.find<NovelGenerationService>();

  // 添加精简生成服务
  late final LightweightGenerationService _lightweightService;

  // 添加生成模式
  final Rx<GenerationMode> generationMode = GenerationMode.lightweight.obs;

  // Add generation stage state
  final Rx<GenerationStage> generationStage = GenerationStage.idle.obs;

  @override
  void onInit() async {
    super.onInit();
    await _initHive();
    await loadNovels(); // 加载保存的小说

    // 初始化精简生成服务
    _lightweightService = LightweightGenerationService(
      apiConfigController: Get.find<ApiConfigController>(),
      vectorizationService: Get.find<NovelVectorizationService>(),
    );
  }

  // 切换生成模式
  void toggleGenerationMode(dynamic mode) {
    // 如果传入的是布尔值，根据布尔值设置模式
    if (mode is bool) {
      generationMode.value =
          mode ? GenerationMode.lightweight : GenerationMode.standard;
    }
    // 如果传入的是GenerationMode枚举，直接设置
    else if (mode is GenerationMode) {
      generationMode.value = mode;
    }

    Get.snackbar(
      '已切换生成模式',
      '当前模式: ${generationMode.value.displayName}\n${generationMode.value.description}',
      duration: const Duration(seconds: 3),
    );
  }

  Future<void> _initHive() async {
    _novelsBox = await Hive.openBox(_novelsBoxName);
    _chaptersBox = await Hive.openBox(_chaptersBoxName);
    _loadGeneratedChapters();
  }

  Future<void> _saveToHive(Novel novel) async {
    try {
      // 确保小说标题不为空
      if (novel.title.isEmpty) {
        throw Exception('小说标题不能为空');
      }

      // 规范化小说标题作为键
      final String safeTitle = novel.title.trim();
      final novelKey = 'novel_$safeTitle';

      print('[DEBUG] 保存小说到Hive，键: $novelKey');
      print('[DEBUG] 小说内容长度: ${novel.content.length}字');
      print('[DEBUG] 小说大纲长度: ${novel.outline.length}字');
      print('[DEBUG] 小说章节数: ${novel.chapters.length}');

      // 确保Hive盒子已打开
      if (!Hive.isBoxOpen(_novelsBoxName)) {
        print('[DEBUG] Hive盒子未打开，尝试重新打开');
        _novelsBox = await Hive.openBox(_novelsBoxName);
      }

      // 保存小说
      await _novelsBox.put(novelKey, novel);

      // 验证保存是否成功
      final savedNovel = _novelsBox.get(novelKey);
      if (savedNovel == null) {
        throw Exception('保存后无法验证小说数据');
      }

      print('[DEBUG] 保存小说成功: ${novel.title}');
    } catch (e, stackTrace) {
      print('[ERROR] 保存到Hive失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');
      rethrow;
    }
  }

  void _loadGeneratedChapters() {
    try {
      final savedChapters = _chaptersBox.get('chapters');
      if (savedChapters != null) {
        if (savedChapters is List) {
          _generatedChapters.value = savedChapters
              .map((chapterData) => chapterData is Chapter
                  ? chapterData
                  : Chapter.fromJson(Map<String, dynamic>.from(chapterData)))
              .toList();
        }
      }
    } catch (e) {
      print('加载章节失败: $e');
      _generatedChapters.clear();
    }
  }

  Future<void> saveChapter(String novelTitle, Chapter chapter) async {
    try {
      // 查找现有小说
      var novel = novels.firstWhere(
        (n) => n.title == novelTitle,
        orElse: () => Novel(
          title: novelTitle,
          genre: selectedGenres.join(','),
          outline: '',
          content: '',
          chapters: [],
          createdAt: DateTime.now(),
        ),
      );

      // 更新或添加章节
      var chapterIndex =
          novel.chapters.indexWhere((c) => c.number == chapter.number);
      if (chapterIndex != -1) {
        novel.chapters[chapterIndex] = chapter;
      } else {
        novel.chapters.add(chapter);
        // 按章节号排序
        novel.chapters.sort((a, b) => a.number.compareTo(b.number));
      }

      // 更新小说内容
      novel.content = novel.chapters.map((c) => c.content).join('\n\n');

      // 更新或添加小说到列表
      var novelIndex = novels.indexWhere((n) => n.title == novelTitle);
      if (novelIndex != -1) {
        novels[novelIndex] = novel;
      } else {
        novels.add(novel);
      }

      // 保存到本地存储
      await _saveToHive(novel);

      // 通知UI更新
      update();
    } catch (e) {
      print('保存章节失败: $e');
      rethrow;
    }
  }

  // 添加缺少的saveNovel方法
  Future<void> saveNovel(Novel novel) async {
    try {
      print('[DEBUG] 开始保存小说到内存列表和Hive...');
      print('[DEBUG] 小说标题: ${novel.title}');
      print('[DEBUG] 小说内容长度: ${novel.content.length}字');
      print('[DEBUG] 当前小说列表数量: ${novels.length}');

      // 检查小说是否已存在（通过ID而不是标题）
      var novelIndex = novels.indexWhere((n) => n.id == novel.id);
      if (novelIndex != -1) {
        print('[DEBUG] 更新现有小说，索引: $novelIndex');
        // 更新现有小说
        novels[novelIndex] = novel;
      } else {
        print('[DEBUG] 添加新小说');
        // 添加新小说
        novels.add(novel);
      }

      print('[DEBUG] 小说列表更新完成，当前数量: ${novels.length}');

      // 保存到本地存储
      print('[DEBUG] 开始保存到Hive...');
      await _saveToHive(novel);
      print('[DEBUG] Hive保存完成');

      // 通知UI更新
      print('[DEBUG] 通知UI更新...');
      update();

      print('[DEBUG] 小说保存完成: ${novel.title}');
    } catch (e, stackTrace) {
      print('[ERROR] 保存小说失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');
      rethrow;
    }
  }

  /// 更新小说
  Future<void> updateNovel(Novel novel) async {
    try {
      // 检查小说是否已存在
      var novelIndex = novels.indexWhere((n) => n.title == novel.title);
      if (novelIndex != -1) {
        // 更新现有小说
        novels[novelIndex] = novel;

        // 保存到本地存储
        await _saveToHive(novel);

        // 通知UI更新
        update();

        print('小说已更新: ${novel.title}');
      } else {
        throw Exception('找不到要更新的小说: ${novel.title}');
      }
    } catch (e) {
      print('更新小说失败: $e');
      rethrow;
    }
  }

  /// 获取当前小说
  Novel? getCurrentNovel() {
    // 如果当前有正在编辑的小说，返回该小说
    if (title.value.isNotEmpty) {
      final novelIndex = novels.indexWhere((n) => n.title == title.value);
      if (novelIndex != -1) {
        return novels[novelIndex];
      }
    }

    // 如果没有正在编辑的小说，返回null
    return null;
  }

  void updateTitle(String value) => title.value = value;
  void updateBackground(String value) => background.value = value;
  void updateOtherRequirements(String value) => otherRequirements.value = value;
  void updateStyle(String value) => style.value = value;
  void updateTargetReader(String value) => targetReader.value = value;

  // 短篇小说相关方法
  void setNovelType(NovelType type) {
    // 暂时禁用短篇小说模式
    if (type == NovelType.shortNovel) {
      Get.snackbar(
        '功能暂不可用',
        '短篇小说功能正在重构中，请选择长篇小说模式',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }
    novelType.value = type;
  }

  void setShortNovelWordCount(ShortNovelWordCount wordCount) {
    shortNovelWordCount.value = wordCount;
  }

  void updateShortNovelWordCount(int count) {
    // 找到最接近的字数选项
    ShortNovelWordCount? closest;
    int minDiff = double.maxFinite.toInt();

    for (final option in ShortNovelWordCount.values) {
      final diff = (option.count - count).abs();
      if (diff < minDiff) {
        minDiff = diff;
        closest = option;
      }
    }

    if (closest != null) {
      shortNovelWordCount.value = closest;
    }
  }

  void updateTotalChapters(int value) {
    if (value > 0) {
      // 如果用户输入的值超过1000，给出提示但仍然允许设置
      if (value > 1000) {
        Get.snackbar(
          '提示',
          '章节数量较多，生成时间可能会较长，建议不要超过1000章',
          duration: const Duration(seconds: 5),
        );
      }
      _totalChapters.value = value;
    } else {
      Get.snackbar('错误', '章节数量必须大于0');
      _totalChapters.value = 1; // 设置为最小值
    }
  }

  void toggleGenre(String genre) {
    if (selectedGenres.contains(genre)) {
      selectedGenres.remove(genre);
    } else if (selectedGenres.length < 5) {
      selectedGenres.add(genre);
    }
  }

  void clearCache() async {
    print('清除所有 LangChain 相关缓存...');
    // _cacheService.clearAllCache(); // 注释掉或保留，取决于这个缓存的作用

    try {
      // 创建一个临时的 NovelMemory 实例并调用 clear
      // 如果有当前会话ID，只清除该会话的数据
      if (_currentSessionId.value.isNotEmpty) {
        await NovelMemory(
                novelTitle: 'temporary_for_clear',
                sessionId: _currentSessionId.value)
            .clear();
        Get.snackbar('缓存已清除', '已清除当前会话的 LangChain 数据。');
      } else {
        // 没有当前会话ID，清除所有数据
        await NovelMemory(novelTitle: 'temporary_for_clear').clear();
        Get.snackbar('缓存已清除', '已清除所有生成的 LangChain 数据。');
      }
    } catch (e) {
      print('清除 LangChain 缓存失败: $e');
      Get.snackbar('错误', '清除缓存失败: $e');
    }

    // 清除会话ID映射
    _novelSessionIds.clear();
    _currentSessionId.value = '';

    // 清除生成进度 (如果需要)
    // _novelGenerator.clearGenerationProgress();
  }

  void _updateRealtimeOutput(String text) {
    if (text.isEmpty) return;

    // 添加日志，帮助调试
    print('更新实时输出: ${text.length} 字符');

    // 确保在主线程更新UI
    Get.engine.addPostFrameCallback((_) {
      realtimeOutput.value += text;
      if (realtimeOutput.value.length > 10000) {
        realtimeOutput.value = realtimeOutput.value.substring(
          realtimeOutput.value.length - 10000,
        );
      }
    });
  }

  void _clearRealtimeOutput() {
    print('清除实时输出');
    realtimeOutput.value = '';
  }

  // 添加新的角色选择相关方法
  void toggleCharacterType(CharacterType type) {
    if (selectedCharacterTypes.contains(type)) {
      selectedCharacterTypes.remove(type);
      // 移除该类型下已选择的角色卡片
      selectedCharacterCards.remove(type.id);
    } else {
      selectedCharacterTypes.add(type);
    }
  }

  void setCharacterCard(String typeId, CharacterCard card) {
    selectedCharacterCards[typeId] = card;
  }

  void removeCharacterCard(String typeId) {
    selectedCharacterCards.remove(typeId);
  }

  // 获取角色设定字符串
  String getCharacterSettings() {
    final buffer = StringBuffer();

    for (final type in selectedCharacterTypes) {
      final card = selectedCharacterCards[type.id];
      if (card != null) {
        buffer.writeln('${type.name}设定：');
        buffer.writeln('姓名：${card.name}');
        if (card.gender.isNotEmpty) {
          buffer.writeln('性别：${card.gender}');
        }
        if (card.age.isNotEmpty) {
          buffer.writeln('年龄：${card.age}');
        }
        if (card.personalityTraits.isNotEmpty) {
          buffer.writeln('性格：${card.personalityTraits}');
        }
        if (card.background.isNotEmpty) {
          buffer.writeln('背景：${card.background}');
        }
        buffer.writeln();
      }
    }

    return buffer.toString();
  }

  /// 导入大纲并转换为JSON格式
  Future<bool> importOutline(String outlineText,
      {bool isDetailedOutline = false}) async {
    if (title.value.isEmpty) {
      Get.snackbar('提示', '请先输入小说标题');
      return false;
    }

    try {
      // 使用增强版大纲导入服务
      final enhancedImportService = Get.find<EnhancedOutlineImportService>();

      // 清除当前大纲
      currentOutline.value = null;
      rawGeneratedOutlineText.value = '';

      // 导入大纲
      final outlineJson = await enhancedImportService.importOutline(
        outlineText: outlineText,
        novelTitle: title.value,
        isDetailedOutline: isDetailedOutline,
        onProgress: (status) {
          // 更新状态
        },
        onChapterProcessed: (processed, total) {
          // 更新进度
        },
      );

      if (outlineJson == null) {
        Get.snackbar('导入失败', '无法解析大纲内容');
        return false;
      }

      // 解析大纲JSON
      final List<ChapterOutline> chapterOutlines = [];

      if (outlineJson.containsKey('chapters') &&
          outlineJson['chapters'] is List) {
        final chapters = outlineJson['chapters'] as List;

        for (final chapter in chapters) {
          if (chapter is Map<String, dynamic>) {
            final chapterNumber = chapter['chapterNumber'] as int? ?? 0;
            final chapterTitle = chapter['chapterTitle'] as String? ?? '无标题';
            final summary = chapter['summary'] as String? ?? '无摘要';

            chapterOutlines.add(ChapterOutline(
              chapterNumber: chapterNumber,
              chapterTitle: chapterTitle,
              contentOutline: summary,
            ));
          }
        }
      }

      // 更新大纲
      currentOutline.value = NovelOutline(
        novelTitle: title.value,
        chapters: chapterOutlines,
      );

      // 生成可读的大纲文本
      final buffer = StringBuffer();
      buffer.writeln('小说标题：${title.value}');
      for (final chOutline in chapterOutlines) {
        buffer.writeln(
            '\n第${chOutline.chapterNumber}章：${chOutline.chapterTitle}');
        buffer.writeln(chOutline.contentOutline);
      }
      rawGeneratedOutlineText.value = buffer.toString();

      // 更新状态
      isUsingOutline.value = true;
      _totalChapters.value = chapterOutlines.length;

      // 重要：将大纲JSON保存到NovelMemory中，以便生成细纲时使用
      try {
        // 获取或创建会话ID
        final sessionId = _getOrCreateSessionId(title.value);
        _currentSessionId.value = sessionId;

        // 将大纲JSON转换为字符串
        final jsonEncoder = JsonEncoder.withIndent('  ');
        final outlineJsonString = jsonEncoder.convert(outlineJson);

        // 保存到NovelMemory - 使用标准化的小说标题
        final normalizedTitle = title.value.trim();

        // 1. 使用会话ID保存一份
        final novelMemoryWithSession =
            NovelMemory(novelTitle: normalizedTitle, sessionId: sessionId);
        await novelMemoryWithSession.saveOutline(outlineJsonString);

        // 2. 不使用会话ID再保存一份，确保在任何情况下都能找到
        final novelMemoryNoSession = NovelMemory(novelTitle: normalizedTitle);
        await novelMemoryNoSession.saveOutline(outlineJsonString);

        // 3. 打印调试信息
        print('大纲已保存到NovelMemory，标题: "$normalizedTitle", 会话ID: $sessionId');
        print('大纲JSON长度: ${outlineJsonString.length}');

        // 将细纲保存为第0章
        await _saveOutlineAsChapterZero();
      } catch (e) {
        // 即使保存失败也不影响导入成功
        print('保存大纲到NovelMemory失败: $e');
      }

      return true;
    } catch (e) {
      Get.snackbar('导入失败', '导入大纲时发生错误: $e');
      return false;
    }
  }

  // 生成或获取会话ID
  String _getOrCreateSessionId(String novelTitle) {
    // 如果已有会话ID，直接返回
    if (_novelSessionIds.containsKey(novelTitle)) {
      final sessionId = _novelSessionIds[novelTitle]!;
      print('使用现有会话ID: $sessionId (小说: $novelTitle)');
      _currentSessionId.value = sessionId;
      return sessionId;
    }

    // 创建新的会话ID
    final sessionId =
        'session_${DateTime.now().millisecondsSinceEpoch}_${novelTitle.hashCode}';
    _novelSessionIds[novelTitle] = sessionId;
    _currentSessionId.value = sessionId;
    print('创建新会话ID: $sessionId (小说: $novelTitle)');
    return sessionId;
  }

  // 增加generateNovel方法用于生成小说
  Future<void> generateNovel({
    bool continueGeneration = false,
  }) async {
    if (isGenerating.value) {
      Get.snackbar('提示', '正在生成中，请稍候...');
      return;
    }

    // 获取或创建会话ID
    final sessionId = _getOrCreateSessionId(title.value);

    if (continueGeneration) {
      // 续写功能已暂时移除
      Get.snackbar('提示', '续写功能已暂时移除，将在后续版本中重新实现。');
      return;
    } else {
      // 根据小说类型选择不同的生成逻辑
      if (novelType.value == NovelType.shortNovel) {
        print('开始生成短篇小说...');
        await generateShortNovel();
      } else {
        // 默认情况下，生成长篇小说
        print('开始生成长篇小说...');

        // 先生成大纲
        if (currentOutline.value == null ||
            currentOutline.value!.chapters.isEmpty) {
          print('开始生成大纲...');
          await generateOutlineWrapper();

          // 等待用户确认大纲
          Get.snackbar('提示', '大纲已生成，请确认后点击"生成细纲"按钮。');
        } else {
          // 如果已有大纲，则开始生成章节
          await startGeneration();
        }
      }
    }
  }

  void addChapter(Chapter chapter) {
    _generatedChapters.add(chapter);
    _sortChapters();
    saveChapter(title.value, chapter);
  }

  void deleteChapter(int chapterNumber) {
    _generatedChapters
        .removeWhere((chapter) => chapter.number == chapterNumber);
    if (novels.isNotEmpty) {
      var novel = novels.firstWhere(
        (n) => n.title == title.value,
        orElse: () => Novel(
          title: title.value,
          genre: selectedGenres.join(','),
          outline: '',
          content: '',
          chapters: [],
          createdAt: DateTime.now(),
        ),
      );

      novel.chapters.removeWhere((chapter) => chapter.number == chapterNumber);
      _saveToHive(novel);
    }
  }

  void clearAllChapters() {
    _generatedChapters.clear();
    if (novels.isNotEmpty) {
      var novel = novels.firstWhere(
        (n) => n.title == title.value,
        orElse: () => Novel(
          title: title.value,
          genre: selectedGenres.join(','),
          outline: '',
          content: '',
          chapters: [],
          createdAt: DateTime.now(),
        ),
      );

      novel.chapters.clear();
      novel.content = '';
      _saveToHive(novel);
    }
  }

  void updateChapter(Chapter chapter) {
    final index =
        _generatedChapters.indexWhere((c) => c.number == chapter.number);
    if (index != -1) {
      _generatedChapters[index] = chapter;
      saveChapter(title.value, chapter);
    }
  }

  Chapter? getChapter(int chapterNumber) {
    return _generatedChapters
        .firstWhereOrNull((chapter) => chapter.number == chapterNumber);
  }

  void _sortChapters() {
    _generatedChapters.sort((a, b) => a.number.compareTo(b.number));
  }

  Future<String> exportChapters(
      String selectedFormat, List<Chapter> selectedChapters) async {
    if (title.isEmpty) {
      return '请先生成小说';
    }

    try {
      final novel = novels.firstWhere((n) => n.title == title.value);
      final result = await _exportService.exportNovel(
        novel,
        selectedFormat,
        selectedChapters: selectedChapters,
      );
      return result;
    } catch (e) {
      return '导出失败：$e';
    }
  }

  // 清除大纲
  void clearOutline() {
    currentOutline.value = null;
    isUsingOutline.value = false;
  }

  // 开始生成小说 (恢复功能)
  Future<void> startGeneration() async {
    if (isGenerating.value) {
      Get.snackbar('提示', '正在生成中，请稍候...');
      return;
    }

    // 检查是否有大纲
    if (currentOutline.value == null ||
        currentOutline.value!.chapters.isEmpty) {
      Get.snackbar('错误', '没有可用的大纲来生成章节。');
      return;
    }

    isGenerating.value = true;
    isPaused.value = false;
    _shouldStop.value = false;
    _clearRealtimeOutput();
    _updateRealtimeOutput('开始生成小说《${title.value}》...\n');
    generationStatus.value = '准备生成...';
    generationProgress.value = 0.0;

    // 检查是否是续写模式 - 如果已有章节，说明是续写模式
    bool isContinuation = _generatedChapters.isNotEmpty;
    int existingChaptersCount = isContinuation ? _generatedChapters.length : 0;

    if (isContinuation) {
      _updateRealtimeOutput("检测到续写模式，已有 $existingChaptersCount 章内容\n");
    }

    final totalChaptersToGenerate = currentOutline.value!.chapters.length;

    try {
      // 计算需要生成的章节数量（排除已有章节）
      int chaptersToGenerate = totalChaptersToGenerate;
      if (isContinuation) {
        chaptersToGenerate = totalChaptersToGenerate - existingChaptersCount;
        _updateRealtimeOutput("需要生成 $chaptersToGenerate 个新章节\n");
      }

      int generatedCount = 0;

      for (int i = 0; i < totalChaptersToGenerate; i++) {
        // 检查是否需要停止
        if (_shouldStop.value || isPaused.value) {
          generationStatus.value = isPaused.value ? '已暂停' : '已停止';
          isGenerating.value = false;
          print(isPaused.value ? '生成已暂停' : '生成已停止');
          return;
        }

        final chapterOutline = currentOutline.value!.chapters[i];
        final chapterNumber = chapterOutline.chapterNumber;

        // 如果是续写模式，跳过已有章节
        if (isContinuation) {
          // 检查章节是否已存在
          bool chapterExists =
              _generatedChapters.any((ch) => ch.number == chapterNumber);
          if (chapterExists) {
            _updateRealtimeOutput(
                "\n跳过第 $chapterNumber 章《${chapterOutline.chapterTitle}》，已有内容\n");
            continue;
          }
        }

        _currentChapter.value = chapterNumber;
        final status =
            '正在生成第 ${_currentChapter.value} 章: ${chapterOutline.chapterTitle}...';
        generationStatus.value = status;
        _updateRealtimeOutput('\n-- $status --\n');

        // 调用 langChain 服务生成章节
        await generateChapterFromOutline(
          chapterNumber: _currentChapter.value,
          // outlineString: null, // 使用 currentOutline.value
          onStatus: (s) => generationStatus.value = s,
          novelTitle: title.value, // 传递当前小说标题
          sessionId: currentSessionId, // 传递当前会话ID
        );

        generatedCount++;

        // 更新进度 - 基于实际需要生成的章节数量
        if (isContinuation) {
          generationProgress.value = generatedCount / chaptersToGenerate;
        } else {
          generationProgress.value = (i + 1) / totalChaptersToGenerate;
        }

        // 短暂延时，避免UI卡顿和API调用过于频繁
        await Future.delayed(const Duration(milliseconds: 500));
      }

      generationStatus.value = '小说生成完成！';
      _updateRealtimeOutput('\n\n小说生成完成！');

      // 设置生成完成状态
      generationStage.value = GenerationStage.generationComplete;

      // 所有章节已在生成过程中实时保存到书库中
      Get.snackbar('生成成功', '小说《${title.value}》已生成完成并保存到书库中');
    } catch (e) {
      generationStatus.value = '生成失败: $e';
      _updateRealtimeOutput('\n\n生成失败: $e');
      print('生成小说时出错: $e');
      // 可以选择显示 Snackbar 提示用户
      Get.snackbar('生成错误', '生成过程中发生错误: $e');
    } finally {
      isGenerating.value = false;
      isPaused.value = false; // 确保结束时重置暂停状态
      _currentChapter.value = 0; // 重置当前章节
    }
  }

  // 检查并继续生成的方法 - 实现续写功能
  Future<void> checkAndContinueGeneration() async {
    if (!isPaused.value) return;

    try {
      // 重置暂停状态
      isPaused.value = false;

      // 如果有当前小说，继续生成
      if (currentOutline.value != null) {
        // 查找未生成的章节
        await continueNovelGeneration();
      } else {
        Get.snackbar('错误', '无法继续生成，未找到当前小说的大纲信息');
        _resetGenerationState();
      }
    } catch (e) {
      Get.snackbar('错误', '操作失败：$e');
      _resetGenerationState();
    }
  }

  /// 检查并继续生成小说 - 用于书库中的续写功能
  /// 返回 'completed' 表示所有章节已生成完毕，'generating' 表示正在生成章节
  Future<String> checkAndContinueNovelGeneration(Novel novel) async {
    try {
      // 设置当前小说信息
      title.value = novel.title;

      // 解析大纲信息
      if (novel.outline.isNotEmpty) {
        // 尝试从小说对象中恢复大纲
        await _recoverOutlineFromNovel(novel);
      }

      // 检查是否有大纲
      if (currentOutline.value == null ||
          currentOutline.value!.chapters.isEmpty) {
        Get.snackbar('错误', '无法续写，未找到小说大纲信息');
        return 'error';
      }

      // 获取大纲中的所有章节
      final outlineChapters = currentOutline.value!.chapters;

      // 获取已生成的章节
      final generatedChapterNumbers =
          novel.chapters.map((ch) => ch.number).toSet();

      // 找出未生成的章节
      final missingChapters = <int>[];
      for (final chapter in outlineChapters) {
        if (!generatedChapterNumbers.contains(chapter.chapterNumber)) {
          missingChapters.add(chapter.chapterNumber);
        }
      }

      // 按章节号排序
      missingChapters.sort();

      if (missingChapters.isEmpty) {
        // 所有章节已生成完毕
        return 'completed';
      }

      // 提示用户有未生成的章节
      final result = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('发现未生成章节'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('检测到以下章节尚未生成：'),
              const SizedBox(height: 8),
              ...missingChapters.map((chapterNumber) {
                final chapterOutline = outlineChapters.firstWhere(
                  (ch) => ch.chapterNumber == chapterNumber,
                );
                return Text(
                    '- 第$chapterNumber章：${chapterOutline.chapterTitle}');
              }),
              const SizedBox(height: 16),
              const Text('需要先补写这些章节才能继续。是否立即开始补写？'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Get.back(result: true),
              child: const Text('开始补写'),
            ),
          ],
        ),
      );

      if (result == true) {
        // 用户选择开始补写
        // 保存当前小说到控制器
        _generatedChapters.clear();
        for (final chapter in novel.chapters) {
          _generatedChapters.add(chapter);
        }

        // 开始生成未生成的章节
        await continueNovelGeneration();
        return 'generating';
      } else {
        // 用户取消补写
        return 'cancelled';
      }
    } catch (e) {
      Get.snackbar('错误', '检查未生成章节失败: $e');
      return 'error';
    }
  }

  /// 从小说对象中恢复大纲 (内部方法)
  Future<void> _recoverOutlineFromNovel(Novel novel) async {
    try {
      // 如果大纲为空，无法恢复
      if (novel.outline.isEmpty) return;

      // 尝试从第0章获取大纲
      final outlineChapter =
          novel.chapters.firstWhereOrNull((ch) => ch.number == 0);
      if (outlineChapter != null) {
        // 从第0章解析大纲
        await _parseOutlineFromChapterZero(outlineChapter.content);
        return;
      }

      // 如果没有第0章，尝试从小说大纲字段解析
      await _parseOutlineFromString(novel.outline);
    } catch (e) {
      print('从小说恢复大纲失败: $e');
    }
  }

  /// 从小说对象恢复大纲 (公开方法，用于续写功能)
  Future<void> restoreOutlineFromNovel(Novel novel) async {
    try {
      // 设置生成阶段为idle，以便后续可以正确处理
      generationStage.value = GenerationStage.idle;

      // 尝试从小说的outline字段恢复大纲
      if (novel.outline.isNotEmpty) {
        // 尝试解析JSON格式的大纲
        try {
          final outlineJson = jsonDecode(novel.outline);
          if (outlineJson is Map<String, dynamic> &&
              outlineJson.containsKey('novelTitle') &&
              outlineJson.containsKey('chapters')) {
            final chapters = <ChapterOutline>[];
            for (final chapterJson in outlineJson['chapters']) {
              chapters.add(ChapterOutline.fromJson(chapterJson));
            }
            currentOutline.value = NovelOutline(
              novelTitle: outlineJson['novelTitle'],
              chapters: chapters,
            );
            print('从小说JSON恢复大纲成功');

            // 设置生成阶段为detailedOutlineReady，表示已有细纲
            generationStage.value = GenerationStage.detailedOutlineReady;
            return;
          }
        } catch (e) {
          print('解析大纲JSON失败，尝试其他方式: $e');
        }

        // 如果JSON解析失败，尝试从文本格式解析
        await _parseOutlineFromString(novel.outline);
        if (currentOutline.value != null) {
          print('从小说文本恢复大纲成功');

          // 设置生成阶段为outlineReady，表示已有大纲
          generationStage.value = GenerationStage.outlineReady;
          return;
        }
      }

      // 如果outline字段为空或解析失败，尝试从第0章内容解析
      if (novel.chapters.isNotEmpty) {
        final chapterZero =
            novel.chapters.firstWhereOrNull((ch) => ch.number == 0);
        if (chapterZero != null && chapterZero.content.isNotEmpty) {
          await _parseOutlineFromChapterZero(chapterZero.content);
          if (currentOutline.value != null) {
            print('从第0章恢复大纲成功');

            // 设置生成阶段为outlineReady，表示已有大纲
            generationStage.value = GenerationStage.outlineReady;
            return;
          }
        }
      }

      // 如果所有尝试都失败，创建一个基本的大纲
      if (currentOutline.value == null && novel.chapters.isNotEmpty) {
        final chapters = <ChapterOutline>[];
        for (final chapter in novel.chapters.where((ch) => ch.number > 0)) {
          chapters.add(ChapterOutline(
            chapterNumber: chapter.number,
            chapterTitle: chapter.title,
            contentOutline: '章节内容概要（自动生成）',
          ));
        }
        if (chapters.isNotEmpty) {
          currentOutline.value = NovelOutline(
            novelTitle: novel.title,
            chapters: chapters,
          );
          print('创建基本大纲成功');

          // 设置生成阶段为detailedOutlineReady，表示已有细纲
          generationStage.value = GenerationStage.detailedOutlineReady;
          return;
        }
      }

      // 如果仍然无法恢复大纲，显示错误
      if (currentOutline.value == null) {
        Get.snackbar('警告', '无法恢复小说大纲，可能需要重新生成');
      } else {
        // 如果成功恢复了大纲，设置生成阶段为detailedOutlineReady
        generationStage.value = GenerationStage.detailedOutlineReady;
      }
    } catch (e) {
      print('从小说恢复大纲失败: $e');
      Get.snackbar('错误', '恢复小说大纲失败: $e');
    }
  }

  /// 准备小说续写 - 恢复大纲并准备扩展
  Future<void> prepareNovelForContinuation(
      Novel novel, int targetTotalChapters) async {
    try {
      // 首先恢复现有大纲
      await restoreOutlineFromNovel(novel);

      // 获取当前大纲中的章节数
      final currentChaptersCount = currentOutline.value?.chapters.length ?? 0;

      // 如果目标章节数小于等于当前章节数，无需扩展大纲
      if (targetTotalChapters <= currentChaptersCount) {
        Get.snackbar(
          '提示',
          '目标章节数 $targetTotalChapters 小于或等于当前大纲章节数 $currentChaptersCount，无需扩展大纲',
          duration: const Duration(seconds: 3),
        );
        return;
      }

      // 保存当前大纲，以便后续扩展
      rawGeneratedOutlineText.value = jsonEncode(currentOutline.value);

      // 设置生成阶段为idle，准备生成新的大纲
      generationStage.value = GenerationStage.idle;

      // 将已有章节加载到生成章节列表中
      _generatedChapters.clear();
      for (final chapter in novel.chapters.where((ch) => ch.number > 0)) {
        _generatedChapters.add(chapter);
      }

      // 向量化已有章节内容，以便后续生成时使用
      try {
        final vectorizationService = Get.find<NovelVectorizationService>();
        if (!vectorizationService.isNovelVectorized(novel.title)) {
          Get.snackbar(
            '正在向量化小说内容',
            '这将帮助AI更好地理解已有内容，生成更连贯的后续章节',
            duration: const Duration(seconds: 3),
          );
          await vectorizationService.vectorizeNovel(novel.title);
        }
      } catch (e) {
        print('向量化小说内容失败: $e');
        // 即使向量化失败，也继续后续步骤
      }

      // 更新总章节数
      _totalChapters.value = targetTotalChapters;

      Get.snackbar(
        '准备完成',
        '已准备好扩展《${novel.title}》到 $targetTotalChapters 章，请点击"生成大纲"按钮开始扩展',
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      print('准备小说续写失败: $e');
      Get.snackbar('错误', '准备小说续写失败: $e');
    }
  }

  /// 从第0章内容解析大纲
  Future<void> _parseOutlineFromChapterZero(String content) async {
    try {
      // 简单解析大纲内容
      final lines = content.split('\n');
      String novelTitle = '';
      final chapters = <ChapterOutline>[];

      int currentChapterNumber = 0;
      String currentChapterTitle = '';
      StringBuffer currentContentOutline = StringBuffer();

      for (final line in lines) {
        if (line.startsWith('小说标题：')) {
          novelTitle = line.substring('小说标题：'.length).trim();
        } else if (line.contains('第') && line.contains('章：')) {
          // 如果已经有章节信息，保存上一章
          if (currentChapterNumber > 0 && currentContentOutline.isNotEmpty) {
            chapters.add(ChapterOutline(
              chapterNumber: currentChapterNumber,
              chapterTitle: currentChapterTitle,
              contentOutline: currentContentOutline.toString().trim(),
            ));
            currentContentOutline.clear();
          }

          // 解析新章节
          final match = RegExp(r'第(\d+)章：(.+)').firstMatch(line);
          if (match != null) {
            currentChapterNumber = int.parse(match.group(1)!);
            currentChapterTitle = match.group(2)!.trim();
          }
        } else if (currentChapterNumber > 0) {
          // 添加到当前章节大纲
          currentContentOutline.writeln(line);
        }
      }

      // 保存最后一章
      if (currentChapterNumber > 0 && currentContentOutline.isNotEmpty) {
        chapters.add(ChapterOutline(
          chapterNumber: currentChapterNumber,
          chapterTitle: currentChapterTitle,
          contentOutline: currentContentOutline.toString().trim(),
        ));
      }

      // 创建大纲对象
      if (novelTitle.isNotEmpty && chapters.isNotEmpty) {
        currentOutline.value = NovelOutline(
          novelTitle: novelTitle,
          chapters: chapters,
        );
      }
    } catch (e) {
      print('解析第0章大纲失败: $e');
    }
  }

  /// 从字符串解析大纲
  Future<void> _parseOutlineFromString(String outlineString) async {
    try {
      // 简单解析大纲字符串
      final lines = outlineString.split('\n');
      String novelTitle = '';
      final chapters = <ChapterOutline>[];

      int currentChapterNumber = 0;
      String currentChapterTitle = '';
      StringBuffer currentContentOutline = StringBuffer();

      for (final line in lines) {
        if (line.startsWith('小说标题：')) {
          novelTitle = line.substring('小说标题：'.length).trim();
        } else if (line.contains('第') && line.contains('章：')) {
          // 如果已经有章节信息，保存上一章
          if (currentChapterNumber > 0 && currentContentOutline.isNotEmpty) {
            chapters.add(ChapterOutline(
              chapterNumber: currentChapterNumber,
              chapterTitle: currentChapterTitle,
              contentOutline: currentContentOutline.toString().trim(),
            ));
            currentContentOutline.clear();
          }

          // 解析新章节
          final match = RegExp(r'第(\d+)章：(.+)').firstMatch(line);
          if (match != null) {
            currentChapterNumber = int.parse(match.group(1)!);
            currentChapterTitle = match.group(2)!.trim();
          }
        } else if (currentChapterNumber > 0) {
          // 添加到当前章节大纲
          currentContentOutline.writeln(line);
        }
      }

      // 保存最后一章
      if (currentChapterNumber > 0 && currentContentOutline.isNotEmpty) {
        chapters.add(ChapterOutline(
          chapterNumber: currentChapterNumber,
          chapterTitle: currentChapterTitle,
          contentOutline: currentContentOutline.toString().trim(),
        ));
      }

      // 创建大纲对象
      if (novelTitle.isNotEmpty && chapters.isNotEmpty) {
        currentOutline.value = NovelOutline(
          novelTitle: novelTitle,
          chapters: chapters,
        );
      }
    } catch (e) {
      print('解析大纲字符串失败: $e');
    }
  }

  // 修改暂停生成的方法
  void stopGeneration() {
    if (!isGenerating.value) return;
    isPaused.value = true;
    _updateRealtimeOutput('\n已暂停生成，当前进度：第${_currentChapter.value}章\n');

    // 保存当前状态，确保暂停状态被正确保存
    Get.snackbar(
      '已暂停',
      '生成已暂停，可以点击"继续生成"按钮恢复',
      duration: const Duration(seconds: 2),
    );
  }

  // 添加开始新小说的方法
  void startNewNovel() {
    // 清除当前小说的状态，但保留所有缓存数据
    print('开始新小说，清除当前状态，但保留缓存');

    // 保存当前会话ID，以便后续使用
    String currentSessionId = _currentSessionId.value;
    String currentTitle = title.value;

    // 清除输入状态
    title.value = '';
    background.value = '';
    otherRequirements.value = '';
    selectedGenres.clear();
    selectedCharacterTypes.clear();
    selectedCharacterCards.clear();

    // 清除生成状态
    _clearRealtimeOutput();
    generationStatus.value = '';
    generationProgress.value = 0.0;
    _currentChapter.value = 0;
    _hasOutline.value = false;

    // 清除大纲状态
    currentOutline.value = null;
    rawGeneratedOutlineText.value = '';
    isUsingOutline.value = false;

    // 清除生成的章节
    _generatedChapters.clear();

    // 清除短篇小说相关状态
    currentShortNovelOutline.value = null;
    shortNovelGeneratedContent.value = '';
    shortNovelCurrentPart.value = 0;

    // 重置生成阶段
    generationStage.value = GenerationStage.idle;

    // 重置当前会话ID，但不清除缓存
    _currentSessionId.value = '';

    // 通知用户
    Get.snackbar(
      '已重置',
      '当前状态已清除，可以开始创作新小说',
      duration: const Duration(seconds: 2),
    );
  }

  // 删除小说
  Future<void> deleteNovel(Novel novel) async {
    try {
      // 从列表中移除
      novels.remove(novel);

      // 从本地存储中删除
      final novelKey = 'novel_${novel.title}';
      await _novelsBox.delete(novelKey);

      // 如果是当前正在生成的小说，清除相关状态
      if (title.value == novel.title) {
        startNewNovel();
      }

      print('删除小说成功: ${novel.title}');
      Get.snackbar('成功', '已删除《${novel.title}》');
    } catch (e) {
      print('删除小说失败: $e');
      Get.snackbar('错误', '删除失败：$e');
    }
  }

  // 加载所有保存的小说
  Future<void> loadNovels() async {
    try {
      print('[DEBUG] 开始加载小说列表');

      // 确保Hive盒子已打开
      if (!Hive.isBoxOpen(_novelsBoxName)) {
        print('[DEBUG] Hive盒子未打开，尝试重新打开');
        _novelsBox = await Hive.openBox(_novelsBoxName);
      }

      // 获取所有以novel_开头的键
      final keys = _novelsBox.keys
          .where((key) => key.toString().startsWith('novel_'))
          .toList();
      print('[DEBUG] 找到 ${keys.length} 个小说键');

      final loadedNovels = <Novel>[];

      for (final key in keys) {
        try {
          final novelData = _novelsBox.get(key);
          print('[DEBUG] 处理键: $key, 数据类型: ${novelData?.runtimeType}');

          if (novelData != null) {
            if (novelData is Novel) {
              print('[DEBUG] 直接加载Novel对象: ${novelData.title}');
              loadedNovels.add(novelData);
            } else if (novelData is Map) {
              print('[DEBUG] 从Map加载Novel: ${novelData['title']}');
              final novel =
                  Novel.fromJson(Map<String, dynamic>.from(novelData));
              loadedNovels.add(novel);
            } else {
              print('[WARNING] 未知数据类型: ${novelData.runtimeType}');
            }
          } else {
            print('[WARNING] 键 $key 对应的数据为null');
          }
        } catch (e, stackTrace) {
          print('[ERROR] 解析小说数据失败，键: $key, 错误: $e');
          print('[ERROR] 错误堆栈: $stackTrace');
        }
      }

      // 按创建时间排序，最新的在前面
      loadedNovels.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // 更新novels列表
      novels.value = loadedNovels;

      print('[DEBUG] 成功加载 ${loadedNovels.length} 本小说');

      // 打印所有加载的小说标题，用于调试
      for (int i = 0; i < loadedNovels.length; i++) {
        print(
            '[DEBUG] 小说 #${i + 1}: ${loadedNovels[i].title}, 内容长度: ${loadedNovels[i].content.length}字');
      }

      // 强制更新UI
      update();
    } catch (e, stackTrace) {
      print('[ERROR] 加载小说失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');

      // 如果加载失败，尝试清空列表并重新初始化
      novels.clear();
      update();
    }
  }

  /// 生成小说大纲
  Future<String> generateOutlineFromTitle({
    required String title,
    required String genre,
    required String theme,
    required String targetReaders,
    required int totalChapters,
  }) async {
    try {
      // 使用LangChain服务生成大纲
      final response = await _langchainService.generateOutline(
        novelTitle: title,
        genres: genre.split(','),
        theme: theme,
        targetReaders: targetReaders,
        totalChapters: totalChapters,
        background: background.value,
        otherRequirements: otherRequirements.value,
        writingStyle: selectedWritingStyle.value,
        characterCards: selectedCharacterCards,
      );
      return response;
    } catch (e) {
      print('生成大纲出错: $e');
      rethrow;
    }
  }

  /// 续写小说 - 检查未生成的章节并补写
  Future<void> continueNovelGeneration() async {
    try {
      // 确保状态正确
      isGenerating.value = true;
      generationStage.value = GenerationStage.generatingChapters;
      generationStatus.value = '正在检查未生成的章节...';

      // 获取当前小说
      final currentNovel = getCurrentNovel();
      if (currentNovel == null || currentOutline.value == null) {
        Get.snackbar('错误', '无法续写，未找到当前小说信息');
        isGenerating.value = false;
        return;
      }

      // 获取大纲中的所有章节
      final outlineChapters = currentOutline.value!.chapters;
      if (outlineChapters.isEmpty) {
        Get.snackbar('提示', '当前小说大纲中没有章节信息');
        isGenerating.value = false;
        return;
      }

      // 获取已生成的章节
      final generatedChapterNumbers =
          currentNovel.chapters.map((ch) => ch.number).toSet();

      // 找出未生成的章节
      final missingChapters = <int>[];
      for (final chapter in outlineChapters) {
        if (!generatedChapterNumbers.contains(chapter.chapterNumber)) {
          missingChapters.add(chapter.chapterNumber);
        }
      }

      // 按章节号排序
      missingChapters.sort();

      if (missingChapters.isEmpty) {
        Get.snackbar('提示', '所有章节已生成完毕，无需续写');
        isGenerating.value = false;
        return;
      }

      // 显示未生成章节的信息
      _clearRealtimeOutput();
      _updateRealtimeOutput('检测到以下未生成的章节：\n');
      for (final chapterNumber in missingChapters) {
        final chapterOutline = outlineChapters.firstWhere(
          (ch) => ch.chapterNumber == chapterNumber,
        );
        _updateRealtimeOutput(
            '- 第${chapterNumber}章：${chapterOutline.chapterTitle}\n');
      }
      _updateRealtimeOutput('\n开始续写未生成的章节...\n\n');

      // 开始生成未生成的章节
      final totalChaptersToGenerate = missingChapters.length;
      generationProgress.value = 0.0;

      for (int i = 0; i < missingChapters.length; i++) {
        // 检查是否需要停止
        if (_shouldStop.value || isPaused.value) {
          generationStatus.value = isPaused.value ? '已暂停' : '已停止';
          isGenerating.value = false;
          print(isPaused.value ? '生成已暂停' : '生成已停止');
          return;
        }

        final chapterNumber = missingChapters[i];
        _currentChapter.value = chapterNumber;
        final status = '正在生成第 ${_currentChapter.value} 章...';
        generationStatus.value = status;
        _updateRealtimeOutput('\n-- $status --\n');

        // 调用生成章节的方法
        await generateChapterFromOutline(
          chapterNumber: chapterNumber,
          onStatus: (s) => generationStatus.value = s,
          novelTitle: currentNovel.title,
          sessionId: currentSessionId,
        );

        // 更新进度
        generationProgress.value = (i + 1) / totalChaptersToGenerate;

        // 短暂延时，避免UI卡顿和API调用过于频繁
        await Future.delayed(const Duration(milliseconds: 500));
      }

      generationStatus.value = '续写完成！';
      _updateRealtimeOutput('\n\n所有未生成章节已补写完成！');

      // 设置生成完成状态
      generationStage.value = GenerationStage.generationComplete;

      // 所有章节已在生成过程中实时保存到书库中
      Get.snackbar('续写成功', '小说《${currentNovel.title}》的未生成章节已补写完成并保存到书库中');
    } catch (e) {
      generationStatus.value = '续写失败: $e';
      _updateRealtimeOutput('\n\n续写失败: $e');
      print('续写小说时出错: $e');
      Get.snackbar('续写错误', '续写过程中发生错误: $e');
    } finally {
      isGenerating.value = false;
      isPaused.value = false; // 确保结束时重置暂停状态
      _currentChapter.value = 0; // 重置当前章节
    }
  }

  /// 从大纲中生成章节内容
  Future<Chapter> generateChapterFromOutline({
    required int chapterNumber,
    String? outlineString,
    Function(String)? onStatus,
    String? novelTitle,
    String? sessionId,
  }) async {
    try {
      onStatus?.call('正在生成第$chapterNumber章...');

      // 处理大纲信息
      String chapterTitle = "第$chapterNumber章";
      String chapterOutlineContent = "";
      String title = novelTitle ?? this.title.value;
      List<String> genres = selectedGenres.toList();

      if (outlineString != null) {
        // 从提供的大纲字符串中提取章节信息
        final chapterPattern = RegExp(
            r'第' + chapterNumber.toString() + r'章[:：](.*?)\n(.*?)(?=第\d+章|$)',
            dotAll: true);
        final match = chapterPattern.firstMatch(outlineString);

        if (match != null) {
          chapterTitle = match.group(1)?.trim() ?? chapterTitle;
          chapterOutlineContent = match.group(2)?.trim() ?? "";
        }
      } else if (currentOutline.value != null) {
        // 从当前大纲对象中获取章节信息
        final chapterOutline = currentOutline.value!.chapters.firstWhereOrNull(
          (ch) => ch.chapterNumber == chapterNumber,
        );

        if (chapterOutline != null) {
          chapterTitle = chapterOutline.chapterTitle;
          chapterOutlineContent = chapterOutline.contentOutline;
        }
      }

      // 根据生成模式选择不同的生成服务
      String content;
      // 使用传入的sessionId或当前会话ID
      final String currentSessionIdToUse = sessionId ?? currentSessionId;

      if (generationMode.value == GenerationMode.lightweight) {
        // 使用精简生成服务
        content = await _lightweightService.generateChapter(
          novelTitle: title,
          chapterNumber: chapterNumber,
          chapterTitle: chapterTitle,
          outlineContent: chapterOutlineContent,
          genres: genres,
          theme: background.value,
          targetReaders: targetReader.value,
          background: background.value,
          otherRequirements: otherRequirements.value,
          writingStyle: selectedWritingStyle.value,
          characterCards: selectedCharacterCards,
          onProgress: (chunk) {
            _updateRealtimeOutput(chunk);
          },
        );

        // 在精简模式下，将细纲保存到NovelMemory
        try {
          final novelMemory =
              NovelMemory(novelTitle: title, sessionId: currentSessionIdToUse);
          await novelMemory.saveChapter(
              chapterNumber, chapterTitle, chapterOutlineContent);
          print('已将第$chapterNumber章细纲保存到NovelMemory');
        } catch (e) {
          print('保存细纲到NovelMemory失败: $e');
        }
      } else {
        // 使用标准生成服务
        content = await _langchainService.generateChapter(
          novelTitle: title,
          chapterNumber: chapterNumber,
          chapterTitle: chapterTitle,
          outlineContent: chapterOutlineContent,
          genres: genres,
          theme: background.value,
          targetReaders: targetReader.value,
          background: background.value,
          otherRequirements: otherRequirements.value,
          writingStyle: selectedWritingStyle.value,
          characterCards: selectedCharacterCards,
          sessionId: currentSessionIdToUse, // 传入会话ID
          onProgress: (chunk) {
            _updateRealtimeOutput(chunk);
          },
        );
      }

      // 创建章节对象
      final chapter = Chapter(
        number: chapterNumber,
        title: chapterTitle,
        content: content,
      );

      // 保存到已生成章节列表
      final existingIndex =
          _generatedChapters.indexWhere((ch) => ch.number == chapterNumber);
      if (existingIndex >= 0) {
        _generatedChapters[existingIndex] = chapter;
      } else {
        _generatedChapters.add(chapter);
        // 确保章节按顺序排序
        _generatedChapters.sort((a, b) => a.number.compareTo(b.number));
      }

      // 保存到Hive
      await _saveChapterToHive(title, chapter);

      // 立即更新小说对象
      await _updateNovelWithCurrentChapters();

      onStatus?.call('第$chapterNumber章生成完成');
      return chapter;
    } catch (e) {
      onStatus?.call('生成失败: $e');
      rethrow;
    }
  }

  /// 获取字符串的最后 N 个字符
  String _getLastNChars(String text, int n) {
    if (text.length <= n) {
      return text;
    }
    return text.substring(text.length - n);
  }

  // 修改重置生成状态的方法
  void _resetGenerationState() {
    isGenerating.value = false;
    isPaused.value = false;
    generationStatus.value = '';
  }

  // 添加 _saveChapterToHive 方法
  Future<void> _saveChapterToHive(String novelTitle, Chapter chapter) async {
    try {
      // 获取已经保存的章节列表
      final chaptersKey = 'chapters_$novelTitle';
      List<dynamic> savedChapters =
          _chaptersBox.get(chaptersKey, defaultValue: []) ?? [];

      // 检查是否存在相同编号的章节
      final index = savedChapters.indexWhere((ch) {
        if (ch is Chapter) {
          return ch.number == chapter.number;
        } else if (ch is Map) {
          return ch['number'] == chapter.number;
        }
        return false;
      });

      // 更新或添加章节
      if (index != -1) {
        savedChapters[index] = chapter;
      } else {
        savedChapters.add(chapter);
      }

      // 保存更新后的章节列表
      await _chaptersBox.put(chaptersKey, savedChapters);

      print('保存章节到 Hive 成功: 第${chapter.number}章 - ${chapter.title}');
    } catch (e) {
      print('保存章节到 Hive 失败: $e');
      rethrow;
    }
  }

  // 添加更新小说大纲的方法
  Future<void> updateNovelOutline(
      String novelTitle, String updatedOutline) async {
    try {
      // 查找现有小说
      var novel = novels.firstWhere(
        (n) => n.title == novelTitle,
        orElse: () => throw Exception('找不到小说: $novelTitle'),
      );

      // 创建更新后的小说副本
      var updatedNovel = novel.copyWith(outline: updatedOutline);

      // 在novels列表中更新
      int index = novels.indexWhere((n) => n.title == novelTitle);
      if (index != -1) {
        novels[index] = updatedNovel;
      }

      // 保存到存储
      await _saveToHive(updatedNovel);

      print('小说大纲更新成功: $novelTitle');
    } catch (e) {
      print('更新小说大纲失败: $e');
      rethrow;
    }
  }

  String _buildGenerationPrompt() {
    final buffer = StringBuffer();

    // 添加文风包提示
    final writingStylePrompt = getWritingStylePrompt();
    if (writingStylePrompt.isNotEmpty) {
      buffer.writeln(writingStylePrompt);
      buffer.writeln();
    }

    // 添加其他提示
    buffer.writeln('''
请根据以下要求创作小说：

标题：$title
类型：${selectedGenres.join('、')}
目标读者：$targetReader
写作风格：$style
''');

    if (background.isNotEmpty) {
      buffer.writeln('故事背景：$background');
    }

    if (otherRequirements.isNotEmpty) {
      buffer.writeln('其他要求：$otherRequirements');
    }

    if (selectedCharacterTypes.isNotEmpty) {
      buffer.writeln('\n角色设定：');
      for (final type in selectedCharacterTypes) {
        final card = selectedCharacterCards[type.id];
        if (card != null) {
          buffer.writeln('''
${type.name}：
- 姓名：${card.name}
- 性别：${card.gender ?? '未设定'}
- 年龄：${card.age ?? '未设定'}
- 性格：${card.personalityTraits ?? '未设定'}
- 背景：${card.background ?? '未设定'}
''');
        }
      }
    }

    return buffer.toString();
  }

  // Wrapper for generating the initial outline (Now displays human-readable text)
  Future<void> generateOutlineWrapper() async {
    if (isGenerating.value || generationStage.value != GenerationStage.idle) {
      Get.snackbar('提示', '正在处理中，请稍候...');
      return;
    }
    if (title.value.isEmpty) {
      Get.snackbar('提示', '请输入故事标题');
      return;
    }

    isGenerating.value = true;
    generationStage.value = GenerationStage.generatingOutline;
    generationStatus.value = '正在生成大纲...'; // Simplified status
    _clearRealtimeOutput();

    // 检查是否是续写模式 - 如果currentOutline已存在，说明是从书库中选择的小说进行续写
    bool isContinuation = currentOutline.value != null;

    // 如果是续写模式，保留当前大纲，否则清空
    if (!isContinuation) {
      currentOutline.value = null;
      rawGeneratedOutlineText.value = '';
    } else {
      _updateRealtimeOutput("检测到续写模式，将基于现有大纲扩展...\n");
      _updateRealtimeOutput(
          "当前大纲包含 ${currentOutline.value!.chapters.length} 章，目标章节数: $_totalChapters\n\n");
    }

    try {
      // 1. Get the JSON array string of chapters from the service
      // 检查是否是续写模式
      bool isContinuation = currentOutline.value != null;
      int existingChaptersCount =
          isContinuation ? currentOutline.value!.chapters.length : 0;

      // 初始化chaptersJsonArrayString
      String chaptersJsonArrayString = "";

      if (isContinuation && existingChaptersCount >= _totalChapters.value) {
        // 如果已有章节数大于等于目标章节数，无需生成新大纲
        _updateRealtimeOutput("当前大纲章节数已满足目标章节数，无需扩展大纲。\n");
        generationStage.value = GenerationStage.outlineReady;
        chaptersJsonArrayString = jsonEncode(currentOutline.value);
      } else if (isContinuation) {
        // 续写模式 - 只生成后续章节的大纲
        _updateRealtimeOutput(
            "续写模式：基于现有大纲扩展，从第${existingChaptersCount + 1}章开始，扩展到第${_totalChapters.value}章...\n");

        // 获取已有大纲的JSON字符串
        String existingOutlineJson = jsonEncode(currentOutline.value);
        _updateRealtimeOutput("已加载现有大纲，包含 $existingChaptersCount 章\n");

        // 检查嵌入模型是否启用
        final apiConfigController = Get.find<ApiConfigController>();
        final vectorizationService = Get.find<NovelVectorizationService>();
        bool embeddingEnabled =
            apiConfigController.embeddingModel.value.enabled;

        // 构建提示词，明确指出这是续写
        String continuationPrompt =
            "这是一个续写任务。请基于已有的$existingChaptersCount章内容，为《${title.value}》生成从第${existingChaptersCount + 1}章到第${_totalChapters.value}章的大纲。保持与已有内容的连贯性和一致性。";

        // 添加已有大纲信息
        continuationPrompt += "\n\n现有大纲信息：\n$existingOutlineJson";

        // 如果嵌入模型启用，获取相关内容
        String relevantContent = "";
        if (embeddingEnabled) {
          _updateRealtimeOutput("嵌入模型已启用，正在获取相关内容...\n");

          // 确保小说已向量化
          if (!vectorizationService.isNovelVectorized(title.value)) {
            _updateRealtimeOutput("小说未向量化，正在向量化...\n");
            await vectorizationService.vectorizeNovel(title.value);
          }

          // 构建优化的续写大纲查询文本
          String queryText = _buildOutlineContinuationQueryText(
            novelTitle: title.value,
            startChapter: existingChaptersCount + 1,
            endChapter: _totalChapters.value,
            genres: selectedGenres.toList(),
            theme: background.value,
            targetReader: targetReader.value,
          );

          // 获取相关内容
          final searchResults = await vectorizationService.searchNovelContent(
            title.value,
            queryText,
            maxResults: apiConfigController.embeddingModel.value.topK,
            includeKnowledgeBase: true,
            includeWritingStyle: true,
          );

          if (searchResults.isNotEmpty) {
            _updateRealtimeOutput("找到 ${searchResults.length} 条相关内容\n");

            // 构建相关内容字符串
            StringBuffer contentBuffer = StringBuffer();
            contentBuffer.writeln("\n\n相关章节内容：");

            for (final result in searchResults) {
              final type = result['type'] as String;
              final content = result['content'] as String;
              final similarity = result['similarity'] as double;

              if (type == 'chapter') {
                final chapterNumber = result['chapter'] as int;
                final chapterTitle = result['title'] as String;

                contentBuffer.writeln(
                    "- 第$chapterNumber章 $chapterTitle (相似度: ${similarity.toStringAsFixed(2)})");
                contentBuffer.writeln("$content\n");
              }
            }

            relevantContent = contentBuffer.toString();
          } else {
            _updateRealtimeOutput("未找到相关内容\n");
          }
        }

        // 将相关内容添加到提示词中
        if (relevantContent.isNotEmpty) {
          continuationPrompt += relevantContent;
        }

        // 根据生成模式选择不同的生成服务
        if (generationMode.value == GenerationMode.lightweight) {
          // 使用精简生成服务
          _updateRealtimeOutput("使用精简生成模式扩展大纲...\n");
          chaptersJsonArrayString = await _lightweightService.generateOutline(
            novelTitle: title.value,
            genres: selectedGenres.toList(),
            theme: background.value,
            targetReaders: targetReader.value,
            totalChapters: _totalChapters.value,
            background: background.value,
            otherRequirements:
                "${otherRequirements.value}\n$continuationPrompt",
            writingStyle: selectedWritingStyle.value,
            characterCards: selectedCharacterCards,
          );
        } else {
          // 使用标准生成服务
          _updateRealtimeOutput("使用标准生成模式扩展大纲...\n");
          chaptersJsonArrayString = await _langchainService.generateOutline(
            novelTitle: title.value,
            genres: selectedGenres.toList(),
            theme: background.value,
            targetReaders: targetReader.value,
            totalChapters: _totalChapters.value,
            background: background.value,
            otherRequirements:
                "${otherRequirements.value}\n$continuationPrompt",
            writingStyle: selectedWritingStyle.value,
            characterCards: selectedCharacterCards,
          );
        }
      } else {
        // 新小说模式 - 生成完整大纲
        // 根据生成模式选择不同的生成服务
        if (generationMode.value == GenerationMode.lightweight) {
          // 使用精简生成服务
          _updateRealtimeOutput("使用精简生成模式生成大纲...\n");
          chaptersJsonArrayString = await _lightweightService.generateOutline(
            novelTitle: title.value,
            genres: selectedGenres.toList(),
            theme: background.value,
            targetReaders: targetReader.value,
            totalChapters: totalChapters,
            background: background.value,
            otherRequirements: otherRequirements.value,
            writingStyle: selectedWritingStyle.value,
            characterCards: selectedCharacterCards,
            onRealtimeOutput: _updateRealtimeOutput,
          );
        } else {
          // 使用标准生成服务
          _updateRealtimeOutput("使用标准生成模式生成大纲...\n");
          chaptersJsonArrayString = await _langchainService.generateOutline(
            novelTitle: title.value,
            genres: selectedGenres.toList(),
            theme: background.value,
            targetReaders: targetReader.value,
            totalChapters: totalChapters,
            background: background.value,
            otherRequirements: otherRequirements.value,
            writingStyle: selectedWritingStyle.value,
            characterCards: selectedCharacterCards,
            onRealtimeOutput: _updateRealtimeOutput,
          );
        }
      }

      print("--- AI Raw Outline Output (expecting JSON Array String) ---");
      print(chaptersJsonArrayString);
      print("----------------------------------------------");

      // 2. Parse the JSON string (could be array or object)
      List<dynamic> chaptersList = [];
      try {
        var decodedJson = jsonDecode(chaptersJsonArrayString);

        // 处理不同的JSON格式
        if (decodedJson is List<dynamic>) {
          // 直接使用数组格式
          chaptersList = decodedJson;
        } else if (decodedJson is Map<String, dynamic>) {
          // 如果是对象格式，提取chapters字段
          print("检测到对象格式的JSON，提取chapters字段");
          if (decodedJson.containsKey('chapters') &&
              decodedJson['chapters'] is List) {
            chaptersList = decodedJson['chapters'] as List<dynamic>;
          } else {
            throw Exception("对象格式的JSON中缺少有效的chapters字段");
          }
        } else {
          throw Exception("无法识别的JSON格式，既不是数组也不是对象");
        }
      } catch (e) {
        print("错误：无法解析从服务返回的JSON: $e");
        print("接收到的字符串: $chaptersJsonArrayString");
        throw Exception("生成的大纲格式无效，无法解析章节列表。");
      }

      // 3. Format the parsed list into human-readable text for UI
      final readableOutlineBuffer = StringBuffer();
      final chapterOutlineObjects = <ChapterOutline>[];
      for (var chapterData in chaptersList) {
        if (chapterData is Map<String, dynamic>) {
          final chapterNumber = chapterData['chapterNumber'] as int? ?? 0;
          final chapterTitle = chapterData['chapterTitle'] as String? ?? '无标题';
          final summary = chapterData['summary'] as String? ??
              '无摘要'; // Assuming key is 'summary' based on previous examples

          readableOutlineBuffer.writeln("第$chapterNumber章：$chapterTitle");
          readableOutlineBuffer.writeln(summary);
          readableOutlineBuffer.writeln(); // Add a blank line between chapters

          // Create ChapterOutline object for internal storage
          chapterOutlineObjects.add(ChapterOutline(
            chapterNumber: chapterNumber,
            chapterTitle: chapterTitle,
            // Assuming ChapterOutline has contentOutline, initialize it empty or with summary?
            contentOutline:
                summary, // For now, let's put summary here as initial content
            // chapterSummary: summary, // If there's a dedicated field
          ));
        } else {
          print("警告：解析出的章节数据格式无效: $chapterData");
        }
      }

      // 4. Update UI with the readable text
      final readableText = readableOutlineBuffer.toString().trim();
      rawGeneratedOutlineText.value = readableText;
      _updateRealtimeOutput(
          "--- 生成的大纲 (预览) ---\n$readableText\n----------------\n");

      // 5. Store the structured outline internally
      currentOutline.value = NovelOutline(
        novelTitle: title.value, // Use the title from the controller state
        chapters: chapterOutlineObjects,
      );
      _totalChapters.value = chapterOutlineObjects
          .length; // Update total chapters based on generated
      isUsingOutline.value = true; // Mark that we have a structured outline

      generationStatus.value = '大纲已生成，请确认。';
      generationStage.value = GenerationStage.outlineReady;
      _updateRealtimeOutput(
          "\n大纲已生成。请预览上方内容，然后点击 \"生成细纲\"。\n(注意：编辑框中的文本仅供预览，修改无效。细纲将基于内部存储的大纲生成)");
    } catch (e) {
      generationStatus.value = '生成大纲失败: $e';
      generationStage.value = GenerationStage.error;
      print('生成大纲时出错: $e');
      Get.snackbar('生成错误', '生成大纲时发生错误: $e');
      _updateRealtimeOutput('\n生成大纲时出错: $e');
    } finally {
      isGenerating.value = false;
    }
  }

  // Stage 2: Generate detailed outline (Now uses internal currentOutline.value)
  Future<void> generateDetailedOutlineFromEdited(
      String editedOutlineText /* This parameter is now ignored */) async {
    // Remove check for generation stage or adjust if needed
    // if (generationStage.value != GenerationStage.outlineReady) { ... }

    if (isGenerating.value) {
      Get.snackbar('提示', '正在处理中，请稍候...');
      return;
    }

    // --- Use the internally stored currentOutline.value ---
    if (currentOutline.value == null) {
      generationStage.value = GenerationStage.idle; // Or error?
      Get.snackbar('错误', '内部大纲数据不存在！请先生成大纲。');
      _updateRealtimeOutput('\n错误：找不到用于生成细纲的内部大纲数据。请重新生成大纲。');
      return;
    }

    // --- Removed parsing logic for editedOutlineText ---
    // NovelOutline? reParsedOutline = currentOutline.value; // Directly use internal state
    // No need to update currentOutline again here, it's already set

    // 2. Start generating detailed outline
    isGenerating.value = true;
    generationStage.value = GenerationStage.generatingDetailedOutline;
    generationStatus.value = '正在生成章节细纲...';
    _clearRealtimeOutput();
    _updateRealtimeOutput('--- 正在根据内部存储的大纲生成细纲 ---\n'); // Updated message

    try {
      // 3. Call LangChain service - Loop through chapters (using currentOutline.value)
      if (currentOutline.value!.chapters.isEmpty) {
        // Null check already done above
        throw Exception("当前内部大纲没有章节。");
      }

      // No need for chaptersToProcess copy if we update the original list via index
      final totalChaptersToProcess = currentOutline.value!.chapters.length;

      // 检查是否是续写模式 - 如果已有章节，说明是续写模式
      bool isContinuation = _generatedChapters.isNotEmpty;
      int existingChaptersCount =
          isContinuation ? _generatedChapters.length : 0;

      if (isContinuation) {
        _updateRealtimeOutput("检测到续写模式，已有 $existingChaptersCount 章内容\n");
        _updateRealtimeOutput(
            "开始为剩余 ${totalChaptersToProcess - existingChaptersCount} 章生成细纲...\n\n");
      } else {
        _updateRealtimeOutput("开始为 $totalChaptersToProcess 章生成细纲...\n\n");
      }

      // 检查嵌入模型是否启用
      final apiConfigController = Get.find<ApiConfigController>();
      final vectorizationService = Get.find<NovelVectorizationService>();
      bool embeddingEnabled = apiConfigController.embeddingModel.value.enabled;

      // 如果嵌入模型启用，确保小说已向量化
      if (embeddingEnabled && isContinuation) {
        if (!vectorizationService.isNovelVectorized(title.value)) {
          _updateRealtimeOutput("小说未向量化，正在向量化...\n");
          await vectorizationService.vectorizeNovel(title.value);
        }
      }

      for (int i = 0; i < totalChaptersToProcess; i++) {
        // Directly access chapter from the reactive list
        final chapter = currentOutline.value!.chapters[i];
        final chapterNumber = chapter.chapterNumber;
        final chapterTitle = chapter.chapterTitle;

        // 如果是续写模式，跳过已有章节的细纲生成
        if (isContinuation && chapterNumber <= existingChaptersCount) {
          _updateRealtimeOutput("\n跳过第 $chapterNumber 章《$chapterTitle》，已有内容");
          continue;
        }

        generationStatus.value =
            '正在生成第 $chapterNumber 章 / 共 $totalChaptersToProcess 章的细纲...';
        _updateRealtimeOutput('\n正在生成第 $chapterNumber 章《$chapterTitle》的细纲...');

        // 准备额外的上下文信息
        String additionalContext = "";

        // 如果是续写模式且嵌入模型启用，获取相关内容
        if (isContinuation && embeddingEnabled) {
          // 构建优化的续写查询文本
          String queryText = _buildContinuationQueryText(
            novelTitle: title.value,
            chapterNumber: chapterNumber,
            chapterTitle: chapterTitle,
            chapterOutline: chapter.contentOutline,
            genres: selectedGenres.toList(),
            theme: background.value,
          );

          // 获取相关内容
          final searchResults = await vectorizationService.searchNovelContent(
            title.value,
            queryText,
            maxResults: apiConfigController.embeddingModel.value.topK,
            includeKnowledgeBase: true,
            includeWritingStyle: true,
          );

          if (searchResults.isNotEmpty) {
            _updateRealtimeOutput("找到 ${searchResults.length} 条相关内容");

            // 构建相关内容字符串
            StringBuffer contentBuffer = StringBuffer();
            contentBuffer.writeln("\n相关章节内容：");

            for (final result in searchResults) {
              final type = result['type'] as String;
              final content = result['content'] as String;

              if (type == 'chapter') {
                final resultChapterNumber = result['chapter'] as int;
                final resultChapterTitle = result['title'] as String;

                contentBuffer
                    .writeln("- 第$resultChapterNumber章 $resultChapterTitle");
                contentBuffer.writeln("$content\n");
              }
            }

            additionalContext = contentBuffer.toString();
          }
        }

        try {
          String detailedMarkdownOutline;

          // 构建提示词
          String promptAddition = "";
          if (isContinuation) {
            promptAddition = "这是一个续写任务，请确保细纲与已有章节内容保持连贯性和一致性。";
            if (additionalContext.isNotEmpty) {
              promptAddition += additionalContext;
            }
          }

          // 根据生成模式选择不同的生成服务
          if (generationMode.value == GenerationMode.lightweight) {
            // 使用精简生成服务
            detailedMarkdownOutline =
                await _lightweightService.generateDetailedChapterOutline(
              novelTitle: currentOutline.value!.novelTitle,
              chapterNumber: chapterNumber,
              chapterTitle: currentOutline.value!.chapters[i].chapterTitle,
              chapterSummary: currentOutline.value!.chapters[i].contentOutline,
              genres: selectedGenres.toList(),
              theme: background.value,
              targetReaders: targetReader.value,
              background: background.value,
              otherRequirements: promptAddition.isNotEmpty
                  ? "${otherRequirements.value}\n\n$promptAddition"
                  : otherRequirements.value,
              writingStyle: selectedWritingStyle.value,
              characterCards: selectedCharacterCards,
            );
          } else {
            // 使用标准生成服务
            detailedMarkdownOutline =
                await _langchainService.generateDetailedChapterOutline(
              novelTitle: currentOutline.value!.novelTitle,
              chapterNumber: chapterNumber,
              genres: selectedGenres.toList(),
              theme: background.value,
              targetReaders: targetReader.value,
              background: background.value,
              otherRequirements: promptAddition.isNotEmpty
                  ? "${otherRequirements.value}\n\n$promptAddition"
                  : otherRequirements.value,
              writingStyle: selectedWritingStyle.value,
              characterCards: selectedCharacterCards,
            );
          }

          // Find the corresponding chapter index (should always be `i` now)
          // FIX: Create a new ChapterOutline instance with the updated contentOutline
          final oldChapter = currentOutline.value!.chapters[i];
          final newChapter = ChapterOutline(
            chapterNumber: oldChapter.chapterNumber,
            chapterTitle: oldChapter.chapterTitle,
            // Keep the original summary/contentOutline from the first stage if needed,
            // or just update the detail here.
            contentOutline:
                detailedMarkdownOutline, // Use the new detailed outline
          );
          // Update the list directly - GetX should handle reactivity
          currentOutline.value!.chapters[i] = newChapter;

          _updateRealtimeOutput('第 $chapterNumber 章细纲生成完毕。');
          // No need to call currentOutline.refresh() explicitly for List updates usually
          // If UI doesn't update, uncomment the refresh() call
          // currentOutline.refresh();
        } catch (e) {
          generationStatus.value = '生成第 $chapterNumber 章细纲失败: $e';
          generationStage.value = GenerationStage.error;
          print('生成第 $chapterNumber 章细纲时出错: $e');
          Get.snackbar('生成错误', '生成第 $chapterNumber 章细纲时发生错误: $e');
          _updateRealtimeOutput('\n生成第 $chapterNumber 章细纲时出错: $e\n停止后续生成。');
          isGenerating.value = false;
          return; // Stop processing further chapters on error
        }
      }

      // 4. Update final state after loop completion
      generationStatus.value = '所有章节细纲已生成，请确认。';
      generationStage.value = GenerationStage.detailedOutlineReady;
      _updateRealtimeOutput('''\n--- 所有章节细纲生成完成 ---
请确认下方细纲，然后点击"生成小说章节"。\n''');
      // Display the final outline with all detailed content
      _displayDetailedOutlineInOutput(currentOutline.value!);

      // 将细纲保存为第0章
      await _saveOutlineAsChapterZero();
    } catch (e) {
      generationStatus.value = '生成细纲失败: $e';
      generationStage.value = GenerationStage.error;
      print('生成细纲时出错: $e');
      Get.snackbar('生成错误', '生成细纲时发生错误: $e');
      _updateRealtimeOutput('\n生成细纲时出错: $e');
    } finally {
      // Ensure isGenerating is set to false
      isGenerating.value = false;
    }
  }

  // Helper to display detailed outline (optional)
  void _displayDetailedOutlineInOutput(NovelOutline detailedOutline) {
    final buffer = StringBuffer();
    buffer.writeln("\n--- 生成的细纲 ---");
    for (final chapter in detailedOutline.chapters) {
      buffer
          .writeln("\n**第${chapter.chapterNumber}章: ${chapter.chapterTitle}**");
      buffer.writeln(
          chapter.contentOutline); // This now contains the detailed plot
    }
    buffer.writeln("\n-----------------");
    _updateRealtimeOutput(buffer.toString());
  }

  // 将细纲保存为第0章
  Future<void> _saveOutlineAsChapterZero() async {
    try {
      if (currentOutline.value == null ||
          currentOutline.value!.chapters.isEmpty) {
        // 无法保存细纲：细纲为空
        return;
      }

      // 构建细纲内容
      final buffer = StringBuffer();
      buffer.writeln('小说标题：${currentOutline.value!.novelTitle}');
      buffer.writeln('生成时间：${DateTime.now().toString()}');
      buffer.writeln('总章节数：${currentOutline.value!.chapters.length}');
      buffer.writeln();

      for (final chOutline in currentOutline.value!.chapters) {
        buffer
            .writeln('第${chOutline.chapterNumber}章：${chOutline.chapterTitle}');
        buffer.writeln(chOutline.contentOutline);
        buffer.writeln();
      }

      final outlineContent = buffer.toString();

      // 创建第0章
      final outlineChapter = Chapter(
        number: 0,
        title: '细纲',
        content: outlineContent,
      );

      // 保存到已生成章节列表
      final existingIndex =
          _generatedChapters.indexWhere((ch) => ch.number == 0);
      if (existingIndex >= 0) {
        _generatedChapters[existingIndex] = outlineChapter;
      } else {
        _generatedChapters.add(outlineChapter);
        // 确保章节按顺序排序
        _generatedChapters.sort((a, b) => a.number.compareTo(b.number));
      }

      // 保存到Hive
      await _saveChapterToHive(title.value, outlineChapter);

      // 创建或更新小说对象
      await _updateNovelWithCurrentChapters();

      // 同时保存每个章节的细纲到NovelMemory
      try {
        // 使用标准化的小说标题
        final normalizedTitle = title.value.trim();
        final sessionId = _getOrCreateSessionId(normalizedTitle);

        // 1. 使用会话ID保存一份
        final novelMemoryWithSession =
            NovelMemory(novelTitle: normalizedTitle, sessionId: sessionId);

        // 2. 不使用会话ID再保存一份，确保在任何情况下都能找到
        final novelMemoryNoSession = NovelMemory(novelTitle: normalizedTitle);

        // 保存每个章节的细纲 - 两份都保存
        for (final chOutline in currentOutline.value!.chapters) {
          // 保存到带会话ID的存储
          await novelMemoryWithSession.saveChapter(chOutline.chapterNumber,
              chOutline.chapterTitle, chOutline.contentOutline);

          // 保存到不带会话ID的存储
          await novelMemoryNoSession.saveChapter(chOutline.chapterNumber,
              chOutline.chapterTitle, chOutline.contentOutline);
        }

        print('已保存所有章节细纲到NovelMemory，标题: "$normalizedTitle", 会话ID: $sessionId');
      } catch (e) {
        // 即使保存到NovelMemory失败，也不影响保存到Hive
        print('保存章节细纲到NovelMemory失败: $e');
      }
    } catch (e) {
      // 保存细纲为第0章时出错
    }
  }

  // 使用当前生成的章节更新小说
  Future<void> _updateNovelWithCurrentChapters() async {
    try {
      if (_generatedChapters.isEmpty) {
        return;
      }

      // 构建大纲字符串
      String outlineString = '';
      if (currentOutline.value != null) {
        final buffer = StringBuffer();
        buffer.writeln('小说标题：${currentOutline.value!.novelTitle}');
        for (final chOutline in currentOutline.value!.chapters) {
          buffer.writeln(
              '第${chOutline.chapterNumber}章：${chOutline.chapterTitle}');
          buffer.writeln(chOutline.contentOutline);
          buffer.writeln();
        }
        outlineString = buffer.toString();
      }

      // 准备要保存的小说数据
      final generatedChaptersList = List<Chapter>.from(_generatedChapters);
      final novelContent =
          generatedChaptersList.map((c) => c.content).join('\n\n');
      final currentDateTime = DateTime.now();

      // 查找现有小说，避免重复创建
      var existingNovelIndex = novels.indexWhere((n) => n.title == title.value);
      Novel novelToSave;

      if (existingNovelIndex != -1) {
        // 如果已存在同名小说，使用 copyWith 更新它
        novelToSave = novels[existingNovelIndex].copyWith(
          chapters: generatedChaptersList, // 更新章节列表
          content: novelContent, // 更新完整内容
          outline: outlineString, // 更新大纲字符串
          updatedAt: currentDateTime, // 更新时间
          style: style.value, // 更新风格
        );
      } else {
        // 如果不存在，创建新的 Novel 对象
        novelToSave = Novel(
          title: title.value,
          genre: selectedGenres.join(','),
          outline: outlineString, // 保存构建的大纲字符串
          content: novelContent,
          chapters: generatedChaptersList,
          createdAt: currentDateTime, // 使用当前时间
          style: style.value, // 保存风格
        );
      }

      // 调用保存方法
      await saveNovel(novelToSave);
      print('小说已更新: ${novelToSave.title}');
    } catch (e) {
      print('更新小说时出错: $e');
    }
  }

  // 短篇小说生成方法
  Future<void> generateShortNovel() async {
    // 禁用短篇小说功能
    Get.snackbar(
      '功能暂不可用',
      '短篇小说功能正在重构中，请选择长篇小说模式',
      backgroundColor: Colors.orange,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
    return;

    // 以下代码暂时禁用
    /*
    if (isGenerating.value) {
      Get.snackbar('提示', '正在生成中，请稍候...');
      return;
    }

    try {
      isGenerating.value = true;
      generationStage.value = GenerationStage.generatingOutline;
      _updateRealtimeOutput('开始生成短篇小说...\n');

      // 1. 生成短篇小说大纲
      await generateShortNovelOutline();

      // 2. 生成短篇小说细纲
      await generateShortNovelDetailedOutline();

      // 3. 生成短篇小说内容
      await generateShortNovelContent();

      generationStage.value = GenerationStage.generationComplete;
      generationStatus.value = '短篇小说生成完成';
      _updateRealtimeOutput('\n短篇小说生成完成！');

      Get.snackbar('成功', '短篇小说《${title.value}》生成完成！');
    } catch (e, stackTrace) {
      print('[ERROR] 短篇小说生成失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');

      generationStatus.value = '生成失败: $e';
      generationStage.value = GenerationStage.error;
      _updateRealtimeOutput('\n生成失败: $e');

      // 提供更友好的错误信息
      String userFriendlyError = '生成过程中遇到问题';
      if (e.toString().contains('timeout')) {
        userFriendlyError = '请求超时，请检查网络连接或稍后重试';
      } else if (e.toString().contains('API')) {
        userFriendlyError = 'API调用失败，请检查配置或稍后重试';
      } else if (e.toString().contains('解析') || e.toString().contains('JSON')) {
        userFriendlyError = '响应解析失败，AI可能返回了格式错误的内容';
      } else if (e.toString().contains('网络')) {
        userFriendlyError = '网络连接问题，请检查网络设置';
      }

      Get.snackbar(
        '生成错误',
        userFriendlyError,
        duration: const Duration(seconds: 5),
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isGenerating.value = false;
    }
    */
  }

  // 检查JSON是否完整
  bool _isCompleteJSON(String content) {
    if (content.trim().isEmpty) return false;

    // 清理内容
    String cleaned = content.trim();
    if (cleaned.startsWith('```json')) {
      cleaned = cleaned.substring(7);
    }
    if (cleaned.endsWith('```')) {
      cleaned = cleaned.substring(0, cleaned.length - 3);
    }
    cleaned = cleaned.trim();

    // 检查基本结构
    if (!cleaned.startsWith('{') || !cleaned.endsWith('}')) {
      return false;
    }

    // 检查括号平衡
    int braceCount = 0;
    int bracketCount = 0;
    bool inString = false;
    bool escaped = false;

    for (int i = 0; i < cleaned.length; i++) {
      final char = cleaned[i];

      if (escaped) {
        escaped = false;
        continue;
      }

      if (char == '\\') {
        escaped = true;
        continue;
      }

      if (char == '"' && !escaped) {
        inString = !inString;
        continue;
      }

      if (!inString) {
        if (char == '{') braceCount++;
        if (char == '}') braceCount--;
        if (char == '[') bracketCount++;
        if (char == ']') bracketCount--;
      }
    }

    // 检查是否包含必要字段
    final hasTitle = cleaned.contains('"title"');
    final hasParts = cleaned.contains('"parts"');
    final hasWordCount = cleaned.contains('"totalWordCount"');

    return braceCount == 0 &&
        bracketCount == 0 &&
        hasTitle &&
        hasParts &&
        hasWordCount;
  }

  // 修复JSON格式问题
  String _fixJsonFormat(String jsonStr) {
    try {
      // 使用正则表达式修复常见的格式问题
      String normalized = jsonStr;

      // 修复缺少空格的问题，如 "key":value -> "key": value
      normalized = normalized.replaceAllMapped(RegExp(r'"([^"]+)":([^,}\s])'),
          (match) => '"${match.group(1)}": ${match.group(2)}');

      // 修复缺少逗号的问题，如 }{ -> },{
      normalized = normalized.replaceAll('}{', '},{');

      // 修复数组元素之间缺少逗号的问题
      normalized = normalized.replaceAllMapped(
          RegExp(r'}(\s*){'), (match) => '},${match.group(1)}{');

      // 修复字符串中的转义问题
      normalized = normalized.replaceAllMapped(
          RegExp(r'\\?"([^"]*)\\"'), (match) => '"${match.group(1)}"');

      // 确保数字值没有引号
      normalized = normalized.replaceAllMapped(
          RegExp(
              r'"(startPercentage|endPercentage|partNumber|totalWordCount|totalParts)"\s*:\s*"(\d+)"'),
          (match) => '"${match.group(1)}": ${match.group(2)}');

      // 修复缺少引号的title字段问题
      normalized = normalized.replaceAllMapped(
        RegExp(r'"title"\s*:\s*([^",\[\]{}]+?)([,\]\}])'),
        (match) {
          String value = match.group(1)?.trim() ?? '';
          if (!value.startsWith('"')) {
            value = '"$value"';
          }
          return '"title": $value${match.group(2)}';
        },
      );

      return normalized;
    } catch (e) {
      // 如果标准化失败，返回原始字符串
      return jsonStr;
    }
  }

  // 使用AI修复JSON格式
  Future<String> _fixJsonWithAI(String brokenJson) async {
    try {
      final fixPrompt = '''
请修复以下JSON格式错误，确保输出有效的JSON：

原始JSON（可能有格式错误）：
$brokenJson

要求：
1. 修复所有JSON格式错误
2. 确保所有字符串值都有双引号
3. 确保数字值没有引号
4. 修复缺少的逗号和引号
5. 只输出修复后的JSON，不要添加任何解释

修复后的JSON：
''';

      final fixedResponse = await _aiService.generateText(
        systemPrompt: '你是一个JSON格式修复专家，专门修复格式错误的JSON数据。',
        userPrompt: fixPrompt,
        temperature: 0.1,
        maxTokens: 2000,
      );

      // 清理AI响应
      String cleaned = fixedResponse.trim();
      if (cleaned.startsWith('```json')) {
        cleaned = cleaned.substring(7);
      }
      if (cleaned.endsWith('```')) {
        cleaned = cleaned.substring(0, cleaned.length - 3);
      }
      cleaned = cleaned.trim();

      return cleaned;
    } catch (e) {
      print('AI修复JSON失败: $e');
      throw Exception('AI修复JSON失败: $e');
    }
  }

  // 生成短篇小说大纲
  Future<void> generateShortNovelOutline() async {
    _updateRealtimeOutput('正在生成短篇小说大纲...\n');
    generationStatus.value = '正在生成短篇小说大纲...';

    try {
      // 根据字数确定分段数量
      final wordCount = shortNovelWordCount.value.count;
      int totalParts;
      if (wordCount <= 5000) {
        totalParts = 3; // 开端、发展、结局
      } else if (wordCount <= 10000) {
        totalParts = 4; // 开端、发展1、发展2、结局
      } else {
        totalParts = 5; // 开端、发展1、发展2、高潮、结局
      }

      // 构建短篇小说大纲生成提示词
      final outlinePrompt =
          _buildShortNovelOutlinePrompt(totalParts, wordCount);

      _updateRealtimeOutput('开始调用AI服务生成大纲...\n');
      print('开始生成短篇小说大纲，提示词长度: ${outlinePrompt.length}');

      // 使用流式输出生成大纲
      final buffer = StringBuffer();
      bool isComplete = false;

      await for (final chunk
          in _aiService.generateShortNovelOutlineStream(outlinePrompt)) {
        buffer.write(chunk);
        _updateRealtimeOutput(chunk); // 实时显示生成过程

        // 检查是否包含完整的JSON结构
        final currentContent = buffer.toString();
        if (_isCompleteJSON(currentContent)) {
          isComplete = true;
        }
      }

      final outlineResponse = buffer.toString();
      _updateRealtimeOutput('\n\n大纲生成完成，开始解析...\n');
      print('大纲生成完成，响应长度: ${outlineResponse.length}');

      // 如果响应看起来不完整，等待一下
      if (!isComplete || outlineResponse.trim().isEmpty) {
        _updateRealtimeOutput('响应可能不完整，等待更多内容...\n');
        await Future.delayed(const Duration(seconds: 2));

        // 如果仍然为空，显示错误信息
        if (outlineResponse.trim().isEmpty) {
          _updateRealtimeOutput('⚠️ 检测到空响应，请检查模型配置或网络连接\n');
        }
      }

      // 尝试解析JSON，如果失败则提供详细错误信息
      Map<String, dynamic> outlineJson;
      try {
        // 清理响应文本，移除可能的markdown标记和注释
        String cleanedResponse = outlineResponse.trim();

        // 移除markdown标记
        if (cleanedResponse.startsWith('```json')) {
          cleanedResponse = cleanedResponse.substring(7);
        }
        if (cleanedResponse.endsWith('```')) {
          cleanedResponse =
              cleanedResponse.substring(0, cleanedResponse.length - 3);
        }

        // 移除注释部分（从"（注："开始到最后）
        int commentIndex = cleanedResponse.indexOf('（注：');
        if (commentIndex != -1) {
          cleanedResponse = cleanedResponse.substring(0, commentIndex);
        }

        // 替换中文标点符号为英文标点符号
        cleanedResponse = cleanedResponse
            .replaceAll('，', ',')
            .replaceAll('：', ':')
            .replaceAll('"', '"')
            .replaceAll('"', '"')
            .replaceAll(''', "'")
            .replaceAll(''', "'");

        // 修复JSON格式问题
        cleanedResponse = _fixJsonFormat(cleanedResponse);

        cleanedResponse = cleanedResponse.trim();

        print(
            '清理后的响应: ${cleanedResponse.substring(0, math.min(500, cleanedResponse.length))}...');
        outlineJson = jsonDecode(cleanedResponse);
      } catch (e) {
        print('JSON解析失败: $e');
        print('原始响应: $outlineResponse');
        _updateRealtimeOutput('JSON解析失败，尝试AI修复...\n');

        // 尝试使用AI修复JSON格式
        try {
          final fixedJson = await _fixJsonWithAI(outlineResponse);
          outlineJson = jsonDecode(fixedJson);
          _updateRealtimeOutput('AI修复JSON成功\n');
        } catch (aiError) {
          _updateRealtimeOutput('AI修复失败: $aiError\n');
          throw Exception(
              '大纲JSON解析失败: $e\n原始响应: ${outlineResponse.substring(0, math.min(200, outlineResponse.length))}...');
        }
      }

      // 验证JSON结构
      if (!outlineJson.containsKey('parts') || outlineJson['parts'] is! List) {
        throw Exception('大纲JSON格式错误：缺少parts数组');
      }

      final parts = <ShortNovelOutlinePart>[];
      final partsList = outlineJson['parts'] as List;

      for (int i = 0; i < partsList.length; i++) {
        try {
          final partData = partsList[i] as Map<String, dynamic>;
          parts.add(ShortNovelOutlinePart.fromJson(partData));
        } catch (e) {
          print('解析第${i + 1}部分失败: $e');
          throw Exception('解析第${i + 1}部分失败: $e');
        }
      }

      currentShortNovelOutline.value = ShortNovelOutline(
        title: title.value,
        parts: parts,
        totalWordCount: wordCount,
        totalParts: totalParts,
      );

      _updateRealtimeOutput('\n短篇小说大纲解析完成！\n');
      _displayShortNovelOutline();
    } catch (e) {
      print('生成短篇小说大纲失败: $e');
      _updateRealtimeOutput('\n生成短篇小说大纲失败: $e\n');
      throw Exception('生成短篇小说大纲失败: $e');
    }
  }

  // 生成短篇小说细纲
  Future<void> generateShortNovelDetailedOutline() async {
    if (currentShortNovelOutline.value == null) {
      throw Exception('请先生成短篇小说大纲');
    }

    _updateRealtimeOutput('\n正在生成短篇小说细纲...\n');
    generationStatus.value = '正在生成短篇小说细纲...';

    try {
      final outline = currentShortNovelOutline.value!;
      final updatedParts = <ShortNovelOutlinePart>[];

      for (int i = 0; i < outline.parts.length; i++) {
        final part = outline.parts[i];
        _updateRealtimeOutput('正在生成第${part.partNumber}部分细纲...\n');

        // 构建细纲生成提示词
        final detailedPrompt =
            _buildShortNovelDetailedOutlinePrompt(part, outline);

        // 使用流式输出生成细纲
        final buffer = StringBuffer();
        await for (final chunk in _aiService
            .generateShortNovelDetailedOutlineStream(detailedPrompt)) {
          buffer.write(chunk);
          _updateRealtimeOutput(chunk); // 实时显示生成过程
        }

        final detailedOutline = buffer.toString();
        updatedParts.add(part.copyWith(detailedOutline: detailedOutline));
        _updateRealtimeOutput('\n第${part.partNumber}部分细纲生成完成\n\n');
      }

      currentShortNovelOutline.value = outline.copyWith(parts: updatedParts);
      _updateRealtimeOutput('\n短篇小说细纲生成完成！\n');
    } catch (e) {
      throw Exception('生成短篇小说细纲失败: $e');
    }
  }

  // 生成短篇小说内容
  Future<void> generateShortNovelContent() async {
    if (currentShortNovelOutline.value == null) {
      throw Exception('请先生成短篇小说大纲和细纲');
    }

    _updateRealtimeOutput('\n开始生成短篇小说内容...\n');
    generationStatus.value = '正在生成短篇小说内容...';

    try {
      final outline = currentShortNovelOutline.value!;
      final contentBuffer = StringBuffer();
      String previousContent = '';

      for (int i = 0; i < outline.parts.length; i++) {
        final part = outline.parts[i];
        shortNovelCurrentPart.value = i + 1;

        _updateRealtimeOutput('正在生成第${part.partNumber}部分内容...\n');

        // 每次生成3000字左右
        final targetWords =
            (outline.totalWordCount / outline.totalParts).round();
        final maxWords = 3000; // 每次最多生成3000字
        final actualTargetWords =
            targetWords > maxWords ? maxWords : targetWords;

        // 构建内容生成提示词，包含完整上下文
        final contentPrompt = _buildShortNovelContentPrompt(
          part,
          outline,
          previousContent,
          actualTargetWords,
        );

        // 使用流式输出生成内容
        final partBuffer = StringBuffer();
        await for (final chunk
            in _aiService.generateShortNovelContentStream(contentPrompt)) {
          partBuffer.write(chunk);
          _updateRealtimeOutput(chunk); // 实时显示生成过程
        }

        final partContent = partBuffer.toString();
        contentBuffer.write(partContent);
        contentBuffer.write('\n\n');
        previousContent = contentBuffer.toString();

        _updateRealtimeOutput(
            '\n第${part.partNumber}部分内容生成完成 (${partContent.length}字)\n\n');

        // 如果目标字数较大，可能需要多次生成
        if (targetWords > maxWords) {
          final remainingWords = targetWords - partContent.length;
          if (remainingWords > 500) {
            _updateRealtimeOutput('继续生成第${part.partNumber}部分剩余内容...\n');

            final continuationPrompt = _buildShortNovelContinuationPrompt(
              part,
              outline,
              previousContent,
              remainingWords,
            );

            // 使用流式输出生成续写内容
            final continuationBuffer = StringBuffer();
            await for (final chunk in _aiService
                .generateShortNovelContentStream(continuationPrompt)) {
              continuationBuffer.write(chunk);
              _updateRealtimeOutput(chunk); // 实时显示生成过程
            }

            final continuationContent = continuationBuffer.toString();
            contentBuffer.write(continuationContent);
            contentBuffer.write('\n\n');
            previousContent = contentBuffer.toString();

            _updateRealtimeOutput(
                '\n第${part.partNumber}部分补充内容生成完成 (${continuationContent.length}字)\n\n');
          }
        }
      }

      shortNovelGeneratedContent.value = contentBuffer.toString();

      print(
          '[DEBUG] 短篇小说内容生成完成，内容长度: ${shortNovelGeneratedContent.value.length}字');
      print(
          '[DEBUG] 内容预览: ${shortNovelGeneratedContent.value.substring(0, math.min(200, shortNovelGeneratedContent.value.length))}...');

      // 保存短篇小说
      print('[DEBUG] 开始调用保存方法...');
      await _saveShortNovel();
      print('[DEBUG] 保存方法调用完成');

      _updateRealtimeOutput(
          '\n短篇小说内容生成完成！总字数: ${shortNovelGeneratedContent.value.length}字\n');
    } catch (e, stackTrace) {
      print('[ERROR] 生成短篇小说内容失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');
      _updateRealtimeOutput('\n生成短篇小说内容失败: $e\n');
      throw Exception('生成短篇小说内容失败: $e');
    }
  }

  // 保存短篇小说
  Future<void> _saveShortNovel() async {
    try {
      print('[DEBUG] 开始保存短篇小说...');
      print('[DEBUG] 小说标题: ${title.value}');
      print('[DEBUG] 生成内容长度: ${shortNovelGeneratedContent.value.length}字');
      print(
          '[DEBUG] 大纲数据: ${currentShortNovelOutline.value != null ? "存在" : "不存在"}');

      final novel = Novel(
        title: title.value,
        genre: selectedGenres.join(','),
        outline: jsonEncode(currentShortNovelOutline.value?.toJson()),
        content: shortNovelGeneratedContent.value,
        chapters: [], // 短篇小说不分章节
        createdAt: DateTime.now(),
        style: style.value,
        sessionId: _currentSessionId.value,
      );

      print('[DEBUG] Novel对象创建成功，准备保存...');
      await saveNovel(novel);
      print('[DEBUG] 短篇小说保存完成');
      _updateRealtimeOutput('短篇小说已保存\n');
    } catch (e, stackTrace) {
      print('[ERROR] 保存短篇小说失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');
      _updateRealtimeOutput('保存短篇小说失败: $e\n');
      rethrow;
    }
  }

  // 显示短篇小说大纲
  void _displayShortNovelOutline() {
    if (currentShortNovelOutline.value == null) return;

    final outline = currentShortNovelOutline.value!;
    _updateRealtimeOutput('\n========== 短篇小说大纲 ==========\n');
    _updateRealtimeOutput('标题: ${outline.title}\n');
    _updateRealtimeOutput('总字数: ${outline.totalWordCount}字\n');
    _updateRealtimeOutput('总部分数: ${outline.totalParts}部分\n\n');

    for (final part in outline.parts) {
      _updateRealtimeOutput('第${part.partNumber}部分: ${part.title}\n');
      _updateRealtimeOutput(
          '进度: ${part.startPercentage}% - ${part.endPercentage}%\n');
      _updateRealtimeOutput('描述: ${part.description}\n\n');
    }

    _updateRealtimeOutput('================================\n\n');
  }

  // 构建角色详情字符串
  String _buildCharacterDetailsString() {
    if (selectedCharacterTypes.isEmpty) {
      return '';
    }

    final buffer = StringBuffer();
    buffer.writeln('\n# 角色设定');

    for (final type in selectedCharacterTypes) {
      final card = selectedCharacterCards[type.id];
      if (card != null) {
        buffer.writeln('## ${card.name} (${type.name})');
        buffer.writeln('- 性别：${card.gender ?? '未设定'}');
        buffer.writeln('- 年龄：${card.age ?? '未设定'}');
        buffer.writeln('- 外貌：${card.appearance ?? '未设定'}');
        buffer.writeln('- 性格：${card.personalityTraits ?? '未设定'}');
        buffer.writeln('- 背景：${card.background ?? '未设定'}');
        buffer.writeln('- 能力：${card.abilities ?? '未设定'}');
        buffer.writeln('- 动机：${card.motivation ?? '未设定'}');
        buffer.writeln('');
      }
    }

    return buffer.toString();
  }

  // 构建写作风格提示词
  String _buildWritingStylePrompt() {
    if (selectedWritingStyle.value == null) {
      return '';
    }

    final package = selectedWritingStyle.value!;
    return '''
# 写作风格要求
请严格按照以下文风进行创作：
- 作者：${package.author}
- 风格描述：${package.description}
- 示例文本：
${package.sampleTexts.join('\n')}

请保持相同的写作风格、语言特点和叙事方式。
''';
  }

  // 构建知识库提示词
  String _buildKnowledgeBasePrompt() {
    try {
      final knowledgeBaseController = Get.find<KnowledgeBaseController>();

      if (!knowledgeBaseController.useKnowledgeBase.value ||
          knowledgeBaseController.selectedDocIds.isEmpty) {
        return '';
      }

      final knowledgeContent = knowledgeBaseController.getSelectedDocsContent();
      if (knowledgeContent.isEmpty) {
        return '';
      }

      return '''
# 专业知识库
请将以下专业知识融入创作中，确保内容的专业性和准确性：

$knowledgeContent

请在创作时自然地运用这些知识，不要生硬地堆砌信息。
''';
    } catch (e) {
      print('获取知识库内容失败: $e');
      return '';
    }
  }

  // 构建短篇小说大纲生成提示词
  String _buildShortNovelOutlinePrompt(int totalParts, int wordCount) {
    final genresText = selectedGenres.join('、');
    final characterText = _buildCharacterDetailsString();

    return '''
请为短篇小说生成大纲：

标题：${title.value}
类型：$genresText
目标读者：${targetReader.value}
总字数：$wordCount字
分段数量：$totalParts部分
背景：${background.value}
要求：${otherRequirements.value}

$characterText

请生成JSON格式的大纲，包含title、totalWordCount、totalParts和parts字段。
parts数组中每个元素包含partNumber、title、description、startPercentage、endPercentage。
description控制在150字以内，百分比合理分配。
''';
  }

  // 构建短篇小说细纲生成提示词
  String _buildShortNovelDetailedOutlinePrompt(
      ShortNovelOutlinePart part, ShortNovelOutline outline) {
    final genresText = selectedGenres.join('、');
    final characterText = _buildCharacterDetailsString();

    return '''
你是一位专业的短篇小说创作助手，请为短篇小说《${outline.title}》的第${part.partNumber}部分生成详细的细纲。

# 小说基本信息
- 标题：${outline.title}
- 类型：$genresText
- 目标读者：${targetReader.value}
- 总字数：${outline.totalWordCount}字
- 总部分数：${outline.totalParts}部分

$characterText

# 当前部分信息
- 部分编号：第${part.partNumber}部分
- 部分标题：${part.title}
- 进度范围：${part.startPercentage}% - ${part.endPercentage}%
- 部分描述：${part.description}

# 整体大纲结构
${outline.parts.map((p) => '第${p.partNumber}部分: ${p.title} (${p.startPercentage}%-${p.endPercentage}%) - ${p.description}').join('\n')}

# 细纲生成要求
请为第${part.partNumber}部分生成详细的细纲，包括：
1. 具体的情节发展
2. 人物行动和对话要点
3. 场景描述要点
4. 情感变化和心理描写
5. 与前后部分的衔接

请确保：
- 细纲内容丰富，为后续内容生成提供充分指导
- 符合短篇小说的节奏和特点
- 与整体大纲保持一致
- 字数分配合理（约${(outline.totalWordCount * (part.endPercentage - part.startPercentage) / 100).round()}字）
- 细纲总字数严格控制在800字以内

# 字数限制要求
**重要：生成的细纲内容必须严格控制在800字以内，请精炼表达，突出重点。**

现在请生成第${part.partNumber}部分的详细细纲（限制800字以内）：
''';
  }

  // 构建短篇小说内容生成提示词
  String _buildShortNovelContentPrompt(
    ShortNovelOutlinePart part,
    ShortNovelOutline outline,
    String previousContent,
    int targetWords,
  ) {
    final genresText = selectedGenres.join('、');
    final characterText = _buildCharacterDetailsString();
    final writingStyleText = _buildWritingStylePrompt();
    final knowledgeBaseText = _buildKnowledgeBasePrompt();

    return '''
你是一位专业的短篇小说作家，请根据以下信息为短篇小说《${outline.title}》的第${part.partNumber}部分生成内容。

# 小说基本信息
- 标题：${outline.title}
- 类型：$genresText
- 目标读者：${targetReader.value}
- 总字数：${outline.totalWordCount}字

$characterText

$writingStyleText

$knowledgeBaseText

# 当前部分信息
- 部分编号：第${part.partNumber}部分
- 部分标题：${part.title}
- 目标字数：约${targetWords}字
- 细纲：${part.detailedOutline}

${previousContent.isNotEmpty ? '''
# 前文内容
${previousContent}

# 衔接要求
请确保与前文内容自然衔接，保持故事连贯性。
''' : ''}

# 创作要求
1. 严格按照细纲内容创作
2. 字数控制在${targetWords}字左右
3. 语言生动，情节紧凑
4. 符合${targetReader.value}的阅读习惯
5. 体现短篇小说的特点：节奏快、冲突强、情感浓
6. 禁用嵌入模型功能

请现在开始创作第${part.partNumber}部分的内容：
''';
  }

  // 构建短篇小说续写提示词
  String _buildShortNovelContinuationPrompt(
    ShortNovelOutlinePart part,
    ShortNovelOutline outline,
    String previousContent,
    int remainingWords,
  ) {
    final genresText = selectedGenres.join('、');

    return '''
你是一位专业的短篇小说作家，请继续为短篇小说《${outline.title}》的第${part.partNumber}部分补充内容。

# 小说基本信息
- 标题：${outline.title}
- 类型：$genresText
- 目标读者：${targetReader.value}

# 当前部分信息
- 部分编号：第${part.partNumber}部分
- 部分标题：${part.title}
- 需要补充字数：约${remainingWords}字
- 细纲：${part.detailedOutline}

# 已有内容
${previousContent}

# 续写要求
1. 自然衔接已有内容
2. 继续按照细纲发展情节
3. 补充字数约${remainingWords}字
4. 保持文风一致
5. 确保情节完整性
6. 禁用嵌入模型功能

请继续创作：
''';
  }

  /// 构建续写模式的优化查询文本
  ///
  /// 专门为续写场景优化，重点关注连贯性和一致性
  String _buildContinuationQueryText({
    required String novelTitle,
    required int chapterNumber,
    required String chapterTitle,
    required String chapterOutline,
    required List<String> genres,
    required String theme,
  }) {
    final buffer = StringBuffer();

    // 1. 明确这是续写任务
    buffer.write('续写小说《$novelTitle》第$chapterNumber章《$chapterTitle》');

    // 2. 添加类型和主题信息
    if (genres.isNotEmpty) {
      buffer.write(' 类型：${genres.join('、')}');
    }
    if (theme.isNotEmpty) {
      buffer.write(' 主题：$theme');
    }

    // 3. 添加章节大纲
    buffer.write(' 章节大纲：$chapterOutline');

    // 4. 强调前文故事情节和人物发展的续写检索需求
    buffer.write(' 续写重点检索前文：');

    // 4.1 前文故事情节深度检索
    buffer.write('关键情节节点和转折、未解决的故事线索、');
    buffer.write('重要事件的前因后果、情节发展的逻辑链条、');
    buffer.write('埋下的伏笔和悬念、故事节奏和氛围、');

    // 4.2 人物发展深度检索
    buffer.write('主要角色的完整发展轨迹、性格成长变化过程、');
    buffer.write('人物关系的演变历史、角色动机和目标变化、');
    buffer.write('重要的人物互动和对话、情感发展脉络、');
    buffer.write('角色的行为模式和思维方式、');

    // 4.3 连贯性关键要素
    buffer.write('角色当前的身心状态、未完成的行动和对话、');
    buffer.write('环境和场景的延续、情感氛围的承接');

    // 5. 根据章节位置添加特定的续写重点
    if (chapterNumber <= 3) {
      buffer.write(' 前期续写重点：保持开篇风格、延续人物设定、维持世界观一致性、');
      buffer.write('前文人物介绍的细节、初始人物关系、开篇氛围的延续');
    } else if (chapterNumber <= 10) {
      buffer.write(' 中期续写重点：推进主线情节、深化人物关系、处理已有冲突、');
      buffer.write('前文重要情节的发展、人物成长的轨迹、冲突升级的过程');
    } else {
      buffer.write(' 后期续写重点：解决核心冲突、呼应前文伏笔、准备故事收尾、');
      buffer.write('前文所有重要线索、人物发展的完整历程、需要解决的所有问题');
    }

    // 6. 根据章节大纲内容添加智能化的续写检索重点
    final intelligentFocus =
        _analyzeContinuationOutlineForQueryFocus(chapterOutline);
    if (intelligentFocus.isNotEmpty) {
      buffer.write(' 智能续写检索重点：$intelligentFocus');
    }

    // 7. 添加续写连贯性目标
    buffer.write(' 续写目标：确保与前文在情节发展、人物行为、情感状态、世界观设定上完全连贯');

    return buffer.toString();
  }

  /// 构建续写大纲的优化查询文本
  ///
  /// 专门为续写大纲生成优化，重点关注整体故事结构和发展方向
  String _buildOutlineContinuationQueryText({
    required String novelTitle,
    required int startChapter,
    required int endChapter,
    required List<String> genres,
    required String theme,
    required String targetReader,
  }) {
    final buffer = StringBuffer();

    // 1. 明确这是续写大纲任务
    buffer.write('续写小说《$novelTitle》大纲 从第$startChapter章到第$endChapter章');

    // 2. 添加类型和主题信息
    if (genres.isNotEmpty) {
      buffer.write(' 类型：${genres.join('、')}');
    }
    if (theme.isNotEmpty) {
      buffer.write(' 主题：$theme');
    }
    if (targetReader.isNotEmpty) {
      buffer.write(' 目标读者：$targetReader');
    }

    // 3. 强调前文故事情节和人物发展的续写大纲检索需求
    buffer.write(' 续写大纲重点检索前文：');

    // 3.1 故事情节结构分析
    buffer.write('已完成的主要情节线、故事发展的整体脉络、');
    buffer.write('重要事件的影响和后续、未解决的核心冲突、');
    buffer.write('故事节奏和张力变化、关键转折点的设置、');

    // 3.2 人物发展轨迹分析
    buffer.write('主要角色的成长历程、人物关系网络的演变、');
    buffer.write('角色目标和动机的变化、重要的人物互动模式、');
    buffer.write('角色性格的发展方向、情感线的发展状态、');

    // 3.3 整体结构规划需求
    buffer.write('故事主题的深化方向、冲突升级的可能路径、');
    buffer.write('高潮设计的铺垫要素、结局走向的伏笔线索');

    // 4. 根据章节范围添加特定的续写重点
    final totalChapters = endChapter - startChapter + 1;
    if (totalChapters <= 5) {
      buffer.write(' 续写重点：短期情节发展、局部冲突解决、人物关系推进');
    } else if (totalChapters <= 15) {
      buffer.write(' 续写重点：中期故事发展、主要冲突升级、关键转折点设计');
    } else {
      buffer.write(' 续写重点：长期故事规划、多线索整合、高潮与结局设计');
    }

    // 5. 添加结构化要求
    buffer.write(' 结构要求：保持节奏平衡、确保逻辑连贯、维持角色一致性');

    return buffer.toString();
  }

  /// 分析续写章节大纲内容，提取智能化的检索重点
  ///
  /// 专门为续写场景优化，重点关注与前文的连接点
  String _analyzeContinuationOutlineForQueryFocus(String chapterOutline) {
    final lowerOutline = chapterOutline.toLowerCase();
    final focusPoints = <String>[];

    // 检测人物互动相关关键词
    final interactionKeywords = [
      '见面',
      '相遇',
      '对话',
      '交流',
      '争吵',
      '和解',
      '分别',
      '重逢'
    ];
    if (interactionKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('相关人物的前文互动历史、关系发展状态、未解决的人际问题');
    }

    // 检测情感发展相关关键词
    final emotionKeywords = ['爱情', '友情', '亲情', '信任', '背叛', '原谅', '误解', '理解'];
    if (emotionKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('相关角色的情感发展历程、情感状态变化、重要的情感转折点');
    }

    // 检测冲突解决相关关键词
    final resolutionKeywords = ['解决', '处理', '面对', '克服', '战胜', '失败', '成功', '结果'];
    if (resolutionKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('前文相关冲突的起源和发展、角色的应对方式、之前的尝试和结果');
    }

    // 检测回忆/揭示相关关键词
    final revelationKeywords = ['回忆', '想起', '发现', '揭示', '真相', '秘密', '过去', '隐瞒'];
    if (revelationKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('相关的历史事件、隐藏的信息、角色的过往经历、重要的背景故事');
    }

    // 检测决定/行动相关关键词
    final actionKeywords = ['决定', '选择', '行动', '计划', '准备', '出发', '前往', '开始'];
    if (actionKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('角色的思考过程、价值观念、影响决策的前文事件、相关的动机发展');
    }

    // 检测转折/变化相关关键词
    final changeKeywords = ['转折', '变化', '改变', '突然', '意外', '惊讶', '震惊', '不同'];
    if (changeKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('前文的铺垫和伏笔、角色的预期和计划、可能的转折点准备');
    }

    // 检测成长/发展相关关键词
    final growthKeywords = ['成长', '学会', '明白', '领悟', '进步', '提升', '突破', '觉醒'];
    if (growthKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('角色的成长轨迹、学习过程、重要的启发时刻、性格发展历程');
    }

    return focusPoints.join('、');
  }
}
