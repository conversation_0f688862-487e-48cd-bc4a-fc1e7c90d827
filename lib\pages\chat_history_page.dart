import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/models/chat_message.dart';
import 'package:novel_app/services/chat_history_service.dart';
import 'package:novel_app/controllers/novel_generation_controller.dart';
import 'package:intl/intl.dart';

class ChatHistoryPage extends StatefulWidget {
  final String novelTitle;

  const ChatHistoryPage({Key? key, required this.novelTitle}) : super(key: key);

  @override
  State<ChatHistoryPage> createState() => _ChatHistoryPageState();
}

class _ChatHistoryPageState extends State<ChatHistoryPage> {
  final ChatHistoryService _chatHistoryService = Get.find<ChatHistoryService>();
  final NovelGenerationController _generationController =
      Get.find<NovelGenerationController>();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  bool _isLoading = false;
  bool _isSending = false;

  @override
  void initState() {
    super.initState();
    _loadChatHistory();
  }

  Future<void> _loadChatHistory() async {
    setState(() {
      _isLoading = true;
    });

    await _chatHistoryService.loadChatHistory(widget.novelTitle);

    setState(() {
      _isLoading = false;
    });

    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    setState(() {
      _isSending = true;
    });

    // 清空输入框
    _messageController.clear();

    // 添加用户消息
    await _chatHistoryService.continueChat(message);

    // 调用AI生成回复
    try {
      final response = await _generationController.generateChatResponse(
          message, widget.novelTitle);

      // 添加AI回复
      await _chatHistoryService.addAIReply(response);
    } catch (e) {
      // 添加错误消息
      await _chatHistoryService.addAIReply("抱歉，生成回复时出现错误: $e");
    }

    setState(() {
      _isSending = false;
    });

    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.novelTitle} - 对话历史'),
        actions: [
          // 聊天设置按钮
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Get.toNamed('/chat_settings', arguments: widget.novelTitle);
            },
            tooltip: '聊天设置',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadChatHistory,
            tooltip: '刷新对话历史',
          ),
          PopupMenuButton(
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'clear',
                child: const Text('清空对话历史'),
                onTap: () async {
                  await _chatHistoryService.clearChatHistory(widget.novelTitle);
                  _loadChatHistory();
                },
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // 对话历史列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : Obx(() {
                    final messages = _chatHistoryService.messages;
                    if (messages.isEmpty) {
                      return const Center(
                        child: Text('暂无对话历史，开始发送消息吧！'),
                      );
                    }

                    return ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16.0),
                      itemCount: messages.length,
                      itemBuilder: (context, index) {
                        final message = messages[index];
                        return _buildMessageItem(message);
                      },
                    );
                  }),
          ),

          // 分隔线
          const Divider(height: 1),

          // 输入框和发送按钮
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: const InputDecoration(
                      hintText: '输入消息...',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16.0,
                        vertical: 12.0,
                      ),
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: 8.0),
                _isSending
                    ? const CircularProgressIndicator()
                    : IconButton(
                        icon: const Icon(Icons.send),
                        onPressed: _sendMessage,
                        tooltip: '发送',
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageItem(ChatMessage message) {
    final isUser = message.type == ChatMessageType.user;
    final isSystem = message.type == ChatMessageType.system;

    // 格式化时间
    final formattedTime = DateFormat('MM-dd HH:mm').format(message.timestamp);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            CircleAvatar(
              backgroundColor:
                  isSystem ? Colors.grey : Theme.of(context).primaryColor,
              child: Icon(
                isSystem ? Icons.info : Icons.smart_toy,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 8.0),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: isUser
                    ? Theme.of(context).primaryColor.withOpacity(0.2)
                    : isSystem
                        ? Colors.grey.withOpacity(0.2)
                        : Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(16.0),
                border: Border.all(
                  color: isUser
                      ? Theme.of(context).primaryColor.withOpacity(0.5)
                      : Colors.grey.withOpacity(0.5),
                  width: 1.0,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.content,
                    style: TextStyle(
                      fontSize: 16.0,
                      color: isSystem ? Colors.grey[700] : null,
                    ),
                  ),
                  const SizedBox(height: 4.0),
                  Text(
                    formattedTime,
                    style: TextStyle(
                      fontSize: 12.0,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 8.0),
            CircleAvatar(
              backgroundColor: Theme.of(context).primaryColor,
              child: const Icon(
                Icons.person,
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
