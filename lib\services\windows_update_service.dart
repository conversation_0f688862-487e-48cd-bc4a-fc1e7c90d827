import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:flutter/foundation.dart' show kIsWeb;

/// Windows平台特定的更新服务
class WindowsUpdateService {
  // 下载状态
  final RxBool isDownloading = false.obs;
  final RxInt downloadProgress = 0.obs;
  final RxString downloadStatus = ''.obs;

  /// 下载并安装更新
  Future<void> downloadAndInstall(String url,
      {Function? onProgress, Function? onComplete, Function? onError}) async {
    if (kIsWeb || !Platform.isWindows) {
      onError?.call('此功能仅支持Windows平台');
      return;
    }

    String? savedFilePath;

    try {
      isDownloading.value = true;
      downloadProgress.value = 0;
      downloadStatus.value = 'downloading';

      // 获取临时目录
      final tempDir = await getTemporaryDirectory();
      final fileName = 'novel_app_update.exe';
      final savePath = path.join(tempDir.path, fileName);

      // 删除可能存在的旧文件
      final saveFile = File(savePath);
      if (await saveFile.exists()) {
        await saveFile.delete();
      }

      // 创建HTTP客户端
      final client = http.Client();

      try {
        // 发送GET请求获取文件大小
        final response = await client.head(Uri.parse(url));
        final totalBytes = int.parse(response.headers['content-length'] ?? '0');

        // 发送GET请求下载文件
        final request = http.Request('GET', Uri.parse(url));
        final streamedResponse = await client.send(request);

        // 创建文件流
        final fileStream = saveFile.openWrite();
        int receivedBytes = 0;

        // 监听下载进度
        await streamedResponse.stream.listen((data) {
          receivedBytes += data.length;
          fileStream.add(data);

          // 计算下载进度
          if (totalBytes > 0) {
            final progress = (receivedBytes / totalBytes * 100).round();
            downloadProgress.value = progress;
            onProgress?.call(progress);
          }
        }).asFuture();

        // 关闭文件流
        await fileStream.flush();
        await fileStream.close();

        // 下载完成
        downloadStatus.value = 'completed';
        isDownloading.value = false;
        downloadProgress.value = 100;

        // 保存文件路径，以便在finally块中使用
        savedFilePath = savePath;

        // 通知调用者下载完成
        onComplete?.call();
      } finally {
        client.close();
      }

      // 如果文件已成功下载，显示安装对话框
      if (savedFilePath != null && await File(savedFilePath).exists()) {
        // 延迟一下，确保进度对话框已关闭
        await Future.delayed(const Duration(milliseconds: 500));
        await _installUpdate(savedFilePath);
      }
    } catch (e) {
      isDownloading.value = false;
      downloadStatus.value = 'error';
      onError?.call('下载更新失败: $e');
      Get.snackbar('更新失败', '下载或安装更新时出错: $e');
    }
  }

  /// 安装更新
  Future<void> _installUpdate(String filePath) async {
    try {
      // 打印日志，帮助调试
      print('准备显示安装对话框，文件路径: $filePath');

      // 检查文件是否存在
      final file = File(filePath);
      if (!await file.exists()) {
        Get.snackbar('安装失败', '安装文件不存在: $filePath');
        return;
      }

      // 显示安装提示
      Get.dialog(
        AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.system_update, color: Colors.blue),
              SizedBox(width: 8),
              Text('安装更新'),
            ],
          ),
          content: const Text(
              '下载完成，正在启动安装程序。\n\n请在安装程序启动后按照提示完成安装。\n\n注意：安装过程中应用将会关闭。'),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('稍后安装'),
            ),
            ElevatedButton(
              onPressed: () async {
                Get.back();
                try {
                  // 打印日志，帮助调试
                  print('启动安装程序: $filePath');

                  // 使用Process.start启动安装程序
                  final process = await Process.start(filePath, []);
                  print('安装程序已启动，进程ID: ${process.pid}');

                  // 延迟一下，确保安装程序已经启动
                  await Future.delayed(const Duration(seconds: 1));

                  // 退出应用，让安装程序可以替换文件
                  exit(0);
                } catch (e) {
                  print('启动安装程序失败: $e');
                  Get.snackbar('启动失败', '无法启动安装程序: $e');
                }
              },
              child: const Text('立即安装'),
            ),
          ],
        ),
        barrierDismissible: true,
      );

      // 打印日志，帮助调试
      print('安装对话框已显示');
    } catch (e) {
      print('显示安装对话框失败: $e');
      Get.snackbar('安装失败', '启动安装程序时出错: $e');
    }
  }

  /// 取消下载
  void cancelDownload() {
    if (isDownloading.value) {
      isDownloading.value = false;
      downloadStatus.value = 'canceled';
      Get.snackbar('已取消', '已取消下载更新');
    }
  }
}
