import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/theme_controller.dart';
import 'package:url_launcher/url_launcher.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('岱宗文脉使用帮助'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHelpSection('API获取', [
              '1. ChatGPT、Claude等国外中转站API获取：https://dzwm.xyz/',
              '2. 阿里百炼获取：https://bailian.console.aliyun.com/',
              '3. deepseek获取：https://platform.deepseek.com/',
              '4. 火山引擎获取：https://console.volcengine.com/ark/region:ark+cn-beijing/model?vendor=Bytedance&view=LIST_VIEW',
              '5. 本地模型部署指南：https://www.bilibili.com/video/BV13e1jY9EmZ?vd_source=21ee701cc82943b9a1e3115e38c3e80c',
              '6. 具体请查看百网盘网盘里的api获取指南：通过网盘分享的文件：https://pan.baidu.com/s/1CzN-Pe4LeS7LVXq6SHYHnA?pwd=xspn 提取码: xspn',
              '7. 没有百度网盘的看夸克网盘里的api获取指南：https://pan.quark.cn/s/0f5f0fb8eebd#/list/share'
            ]),
            const SizedBox(height: 16),
            _buildHelpSection('基本使用', [
              '1. 只有标题是必填项，其余参数如果没有思路可以空着',
              '2. 小说生成完成后去“我的书库”里查看，可以进行续写、导出和编辑',
              '3. 可以在模块仓库中自定义写作风格、角色卡牌、角色类型、小说类型',
              '4. 保存作品：所有创作内容均本地存储，保证隐私'
            ]),
            const SizedBox(height: 16),
            _buildHelpSection('模型推荐', [
              '1. gpt4.1指令遵循，性能好，模型上下文长度达100万',
              '2. deepseek-v3中文写作好，缺点是上下文长度只六万，只适合写短篇，精简生成模式下也只能写90章节',
              '3. gemini2.5flash：免费，性能强，上下文长度达100万（缺点：暂时不能调用，请等待4.2.2版本）',
              '4. 阿里的qwen plus，max等也不错上下文长度12万，阿里的100万上下文长度的模型不建议使用了，性能不行，容易重复',
              '5. 其余的我就没有用过了，大家可以自己测试，也可以自己微调'
            ]),
            const SizedBox(height: 16),
            _buildHelpSection('问题反馈&&商务合作', [
              '路径1：小红书直接私信',
              '路径2：微信公众号：智流行AI，后台留言',
              '路径3：邮箱：<EMAIL>',
              '考研党，回复不及时请见谅，看到就会回复的。有没有计算机院的导师[狗头]'
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildHelpSection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...items.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Text(item),
            )),
        const SizedBox(height: 16),
      ],
    );
  }
}
