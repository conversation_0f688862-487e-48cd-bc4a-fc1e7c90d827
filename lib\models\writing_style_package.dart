import 'package:hive/hive.dart';

part 'writing_style_package.g.dart';

@HiveType(typeId: 5)
class WritingStylePackage {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final List<String> sampleTexts;

  @HiveField(4)
  final String author;

  @HiveField(5)
  final DateTime createdAt;

  @HiveField(6)
  final DateTime updatedAt;

  WritingStylePackage({
    required this.id,
    required this.name,
    required this.description,
    required this.sampleTexts,
    required this.author,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WritingStylePackage.fromJson(Map<String, dynamic> json) {
    return WritingStylePackage(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      sampleTexts: List<String>.from(json['sampleTexts']),
      author: json['author'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'sampleTexts': sampleTexts,
      'author': author,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  WritingStylePackage copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? sampleTexts,
    String? author,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return WritingStylePackage(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      sampleTexts: sampleTexts ?? this.sampleTexts,
      author: author ?? this.author,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String getPrompt() {
    if (name.isEmpty && description.isEmpty && sampleTexts.isEmpty) {
      return ''; // Return empty if package is essentially empty
    }
    final buffer = StringBuffer();
    buffer.writeln('请参考以下文风示例进行创作：');
    if (author.isNotEmpty) buffer.writeln('作者：$author');
    if (description.isNotEmpty) buffer.writeln('风格描述：$description');
    if (sampleTexts.isNotEmpty) {
      buffer.writeln('示例文本：');
      buffer.writeln(sampleTexts.join('\n'));
    }
    buffer.writeln('\n请严格按照上述文风进行创作，保持相同的写作风格、语言特点和叙事方式。');
    return buffer.toString();
  }
}
