package com.daizhong.novelapp

import android.Manifest
import android.app.Activity
import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.core.content.FileProvider
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.net.HttpURLConnection
import java.net.URL
import java.util.concurrent.Executors

class AppUpdateManager(private val context: Context) {
    private val TAG = "AppUpdateManager"
    private var downloadId: Long = -1
    private var downloadReceiver: BroadcastReceiver? = null
    private var updateCallback: ((Map<String, Any>) -> Unit)? = null
    private val executor = Executors.newSingleThreadExecutor()

    /**
     * 检查更新
     * @param updateUrl 主要更新服务器URL
     * @param backupUpdateUrl 备用更新服务器URL
     * @param currentVersion 当前版本号
     * @param callback 回调函数，返回检查结果
     */
    fun checkForUpdates(updateUrl: String, backupUpdateUrl: String?, currentVersion: String, callback: (Map<String, Any>) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 首先尝试主要URL
                try {
                    val versionInfo = fetchVersionInfo(updateUrl)
                    processVersionInfo(versionInfo, currentVersion, callback)
                    return@launch
                } catch (e: Exception) {
                    Log.e(TAG, "主服务器检查更新失败，尝试备用服务器", e)

                    // 如果备用URL可用，尝试备用URL
                    if (!backupUpdateUrl.isNullOrEmpty()) {
                        try {
                            val versionInfo = fetchVersionInfo(backupUpdateUrl)
                            processVersionInfo(versionInfo, currentVersion, callback)
                            return@launch
                        } catch (e2: Exception) {
                            Log.e(TAG, "备用服务器检查更新也失败", e2)
                            throw e2 // 抛出异常，进入下面的catch块
                        }
                    } else {
                        throw e // 没有备用URL，抛出原始异常
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查更新失败", e)
                val result = mutableMapOf<String, Any>()
                result["hasUpdate"] = false
                result["error"] = e.message ?: "未知错误"

                // 在主线程中回调
                withContext(Dispatchers.Main) {
                    callback(result)
                }
            }
        }
    }

    /**
     * 处理版本信息
     * @param versionInfo 版本信息
     * @param currentVersion 当前版本号
     * @param callback 回调函数
     */
    private suspend fun processVersionInfo(versionInfo: JSONObject, currentVersion: String, callback: (Map<String, Any>) -> Unit) {
        val serverVersion = versionInfo.getString("version")
        val hasUpdate = isNewerVersion(serverVersion, currentVersion)

        val result = mutableMapOf<String, Any>()
        result["hasUpdate"] = hasUpdate
        result["version"] = serverVersion
        result["downloadUrl"] = versionInfo.getString("downloadUrl")
        result["releaseNotes"] = versionInfo.getString("releaseNotes")
        result["forceUpdate"] = versionInfo.optBoolean("forceUpdate", false)

        if (versionInfo.has("buildNumber")) {
            result["buildNumber"] = versionInfo.getString("buildNumber")
        }
        if (versionInfo.has("releaseDate")) {
            result["releaseDate"] = versionInfo.getString("releaseDate")
        }

        // 在主线程中回调
        withContext(Dispatchers.Main) {
            callback(result)
        }
    }

    /**
     * 下载更新
     * @param downloadUrl 下载URL
     * @param version 版本号
     * @param callback 回调函数，返回下载进度和结果
     */
    fun downloadUpdate(downloadUrl: String, version: String, callback: (Map<String, Any>) -> Unit) {
        Log.d(TAG, "开始下载更新: url=$downloadUrl, version=$version")
        updateCallback = callback

        try {
            // 我们使用应用专用目录，不需要存储权限
            Log.d(TAG, "使用应用专用目录下载，不需要存储权限")

            // 确保下载目录存在
            val downloadDir = context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)
            if (downloadDir == null) {
                Log.e(TAG, "无法获取下载目录")
                val result = mutableMapOf<String, Any>()
                result["status"] = "error"
                result["error"] = "无法获取下载目录"

                CoroutineScope(Dispatchers.Main).launch {
                    callback(result)
                }
                return
            }

            if (!downloadDir.exists()) {
                val created = downloadDir.mkdirs()
                if (!created) {
                    Log.e(TAG, "无法创建下载目录: ${downloadDir.absolutePath}")
                    val result = mutableMapOf<String, Any>()
                    result["status"] = "error"
                    result["error"] = "无法创建下载目录"

                    CoroutineScope(Dispatchers.Main).launch {
                        callback(result)
                    }
                    return
                }
            }

            // 检查是否已有相同文件名的APK
            val apkFile = File(downloadDir, "novel_app_$version.apk")
            if (apkFile.exists()) {
                Log.d(TAG, "发现已下载的APK文件，删除它")
                apkFile.delete()
            }

            // 创建下载请求
            val uri = Uri.parse(downloadUrl)
            val request = DownloadManager.Request(uri)
                .setTitle("岱宗文脉更新")
                .setDescription("正在下载版本 $version")
                .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
                .setDestinationInExternalFilesDir(context, Environment.DIRECTORY_DOWNLOADS, "novel_app_$version.apk")
                .setAllowedOverMetered(true)
                .setAllowedOverRoaming(true)

            Log.d(TAG, "下载请求已创建")

            // 获取下载管理器
            val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            downloadId = downloadManager.enqueue(request)
            Log.d(TAG, "下载已加入队列，downloadId=$downloadId")

            if (downloadId == -1L) {
                Log.e(TAG, "下载ID无效")
                val result = mutableMapOf<String, Any>()
                result["status"] = "error"
                result["error"] = "下载管理器返回无效的下载ID"

                CoroutineScope(Dispatchers.Main).launch {
                    callback(result)
                }
                return
            }

            // 注册下载完成广播接收器
            registerDownloadReceiver(version)
            Log.d(TAG, "下载完成广播接收器已注册")

            // 启动下载进度监控
            monitorDownloadProgress(downloadManager, downloadId, callback)
            Log.d(TAG, "下载进度监控已启动")

            // 返回初始状态
            val result = mutableMapOf<String, Any>()
            result["status"] = "downloading"
            result["progress"] = 0

            // 在主线程中回调
            CoroutineScope(Dispatchers.Main).launch {
                Log.d(TAG, "发送初始下载状态")
                callback(result)
            }
        } catch (e: Exception) {
            Log.e(TAG, "下载更新失败", e)
            val result = mutableMapOf<String, Any>()
            result["status"] = "error"
            result["error"] = e.message ?: "未知错误"

            // 在主线程中回调
            CoroutineScope(Dispatchers.Main).launch {
                callback(result)
            }
        }
    }

    /**
     * 安装APK
     * @param apkFile APK文件
     */
    fun installApk(apkFile: File) {
        try {
            if (!apkFile.exists()) {
                Log.e(TAG, "APK文件不存在: ${apkFile.absolutePath}")

                // 通知安装失败
                CoroutineScope(Dispatchers.Main).launch {
                    val result = mutableMapOf<String, Any>()
                    result["status"] = "error"
                    result["error"] = "APK文件不存在: ${apkFile.absolutePath}"
                    updateCallback?.invoke(result)
                }

                return
            }

            // 检查文件大小是否合理
            val fileSize = apkFile.length()
            if (fileSize < 1024 * 1024) { // 小于1MB的文件可能是损坏的
                Log.e(TAG, "APK文件大小异常: $fileSize 字节，可能下载不完整")

                // 通知安装失败
                CoroutineScope(Dispatchers.Main).launch {
                    val result = mutableMapOf<String, Any>()
                    result["status"] = "error"
                    result["error"] = "APK文件大小异常: $fileSize 字节，可能下载不完整"
                    updateCallback?.invoke(result)

                    // 显示Toast提示
                    Toast.makeText(context, "安装包可能不完整，请重新下载", Toast.LENGTH_LONG).show()
                }

                return
            }

            Log.d(TAG, "准备安装APK: ${apkFile.absolutePath}, 文件大小: $fileSize 字节")

            // 检查安装权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val packageManager = context.packageManager
                if (!packageManager.canRequestPackageInstalls()) {
                    Log.e(TAG, "没有安装未知来源应用的权限")

                    // 通知权限错误
                    CoroutineScope(Dispatchers.Main).launch {
                        val result = mutableMapOf<String, Any>()
                        result["status"] = "error"
                        result["error"] = "没有安装未知来源应用的权限，请在设置中授予权限"
                        result["code"] = "PERMISSION_REQUIRED"
                        updateCallback?.invoke(result)
                    }

                    // 不再自动打开权限设置，而是通过Flutter端处理
                    return
                } else {
                    Log.d(TAG, "已有安装未知来源应用的权限")
                }
            }

            val intent = Intent(Intent.ACTION_VIEW)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Android 7.0及以上需要使用FileProvider
                try {
                    val uri = FileProvider.getUriForFile(
                        context,
                        "${context.packageName}.fileprovider",
                        apkFile
                    )
                    Log.d(TAG, "使用FileProvider创建URI: $uri")
                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    intent.setDataAndType(uri, "application/vnd.android.package-archive")
                } catch (e: Exception) {
                    Log.e(TAG, "创建FileProvider URI失败", e)

                    // 通知错误
                    CoroutineScope(Dispatchers.Main).launch {
                        val result = mutableMapOf<String, Any>()
                        result["status"] = "error"
                        result["error"] = "创建FileProvider URI失败: ${e.message}"
                        updateCallback?.invoke(result)
                    }

                    return
                }
            } else {
                // Android 7.0以下直接使用文件URI
                intent.setDataAndType(
                    Uri.fromFile(apkFile),
                    "application/vnd.android.package-archive"
                )
            }

            try {
                Log.d(TAG, "启动安装界面")

                // 确保在主线程中启动Activity
                if (context is Activity) {
                    context.runOnUiThread {
                        try {
                            context.startActivity(intent)
                            Log.d(TAG, "安装界面已启动")

                            // 通知前端安装已开始
                            val result = mutableMapOf<String, Any>()
                            result["status"] = "installing"
                            result["message"] = "正在打开安装界面"
                            updateCallback?.invoke(result)

                            // 显示Toast提示
                            Toast.makeText(context, "请在安装界面点击\"安装\"", Toast.LENGTH_LONG).show()
                        } catch (e: Exception) {
                            Log.e(TAG, "在UI线程启动安装界面失败", e)

                            // 通知错误
                            val result = mutableMapOf<String, Any>()
                            result["status"] = "error"
                            result["error"] = "启动安装界面失败: ${e.message}"
                            updateCallback?.invoke(result)

                            Toast.makeText(context, "安装失败: ${e.message}", Toast.LENGTH_LONG).show()
                        }
                    }
                } else {
                    // 如果context不是Activity，添加FLAG_ACTIVITY_NEW_TASK标志
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(intent)
                    Log.d(TAG, "非Activity context启动安装界面")

                    // 通知前端安装已开始
                    CoroutineScope(Dispatchers.Main).launch {
                        val result = mutableMapOf<String, Any>()
                        result["status"] = "installing"
                        result["message"] = "正在打开安装界面"
                        updateCallback?.invoke(result)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "启动安装界面失败", e)

                // 通知错误
                CoroutineScope(Dispatchers.Main).launch {
                    val result = mutableMapOf<String, Any>()
                    result["status"] = "error"
                    result["error"] = "启动安装界面失败: ${e.message}"
                    updateCallback?.invoke(result)

                    // 显示Toast提示
                    Toast.makeText(context, "安装失败: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "安装APK失败", e)

            // 通知错误
            CoroutineScope(Dispatchers.Main).launch {
                val result = mutableMapOf<String, Any>()
                result["status"] = "error"
                result["error"] = "安装APK失败: ${e.message}"
                updateCallback?.invoke(result)
            }

            Toast.makeText(context, "安装失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * 取消下载
     */
    fun cancelDownload() {
        if (downloadId != -1L) {
            val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            downloadManager.remove(downloadId)
            downloadId = -1L
        }

        // 取消注册广播接收器
        unregisterDownloadReceiver()
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        cancelDownload()
        updateCallback = null
        executor.shutdown()
    }

    /**
     * 从服务器获取版本信息
     * @param updateUrl 更新服务器URL
     * @return JSONObject 版本信息
     */
    @Throws(IOException::class)
    private suspend fun fetchVersionInfo(updateUrl: String): JSONObject = withContext(Dispatchers.IO) {
        var connection: HttpURLConnection? = null
        try {
            Log.d(TAG, "开始获取版本信息: $updateUrl")
            val url = URL(updateUrl)
            connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = 15000 // 增加超时时间
            connection.readTimeout = 15000 // 增加超时时间
            connection.instanceFollowRedirects = true // 允许重定向

            // 设置请求头
            connection.setRequestProperty("User-Agent", "DaiZhongNovelApp/${context.packageManager.getPackageInfo(context.packageName, 0).versionName}")
            connection.setRequestProperty("Accept", "application/json")

            Log.d(TAG, "连接服务器...")
            val responseCode = connection.responseCode
            Log.d(TAG, "服务器响应码: $responseCode")

            if (responseCode == HttpURLConnection.HTTP_OK) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                Log.d(TAG, "服务器响应: $response")
                JSONObject(response)
            } else {
                val errorMessage = connection.errorStream?.bufferedReader()?.use { it.readText() } ?: "未知错误"
                Log.e(TAG, "服务器响应错误: $responseCode, $errorMessage")
                throw IOException("服务器响应错误: $responseCode, $errorMessage")
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取版本信息失败", e)
            when (e) {
                is IOException -> throw e
                else -> throw IOException("获取版本信息失败: ${e.message}", e)
            }
        } finally {
            connection?.disconnect()
        }
    }

    /**
     * 比较版本号
     * @param serverVersion 服务器版本号
     * @param currentVersion 当前版本号
     * @return Boolean 是否有新版本
     */
    private fun isNewerVersion(serverVersion: String, currentVersion: String): Boolean {
        val serverParts = serverVersion.split(".")
        val currentParts = currentVersion.split(".")

        // 比较主要版本号
        for (i in 0 until minOf(serverParts.size, currentParts.size)) {
            val serverPart = serverParts[i].toIntOrNull() ?: 0
            val currentPart = currentParts[i].toIntOrNull() ?: 0

            if (serverPart > currentPart) {
                return true
            } else if (serverPart < currentPart) {
                return false
            }
        }

        // 如果前面的版本号都相同，但服务器版本有更多的部分，则认为是新版本
        return serverParts.size > currentParts.size
    }

    /**
     * 注册下载完成广播接收器
     * @param version 版本号
     */
    private fun registerDownloadReceiver(version: String) {
        // 先取消注册之前的接收器
        unregisterDownloadReceiver()

        // 创建新的接收器
        downloadReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
                if (id == downloadId) {
                    val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
                    val query = DownloadManager.Query().setFilterById(downloadId)
                    val cursor = downloadManager.query(query)

                    if (cursor.moveToFirst()) {
                        val statusIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS)
                        val status = cursor.getInt(statusIndex)

                        when (status) {
                            DownloadManager.STATUS_SUCCESSFUL -> {
                                // 获取下载的文件
                                val localUriIndex = cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI)
                                val localUri = cursor.getString(localUriIndex)
                                val fileUri = Uri.parse(localUri)
                                val path = if (fileUri.scheme == "file") {
                                    fileUri.path
                                } else {
                                    // 从content URI获取文件路径
                                    val downloadedFile = File(
                                        context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS),
                                        "novel_app_$version.apk"
                                    )
                                    downloadedFile.absolutePath
                                }

                                val file = path?.let { File(it) }
                                if (file != null && file.exists()) {
                                    Log.d(TAG, "广播接收器：文件存在，大小: ${file.length()} 字节")
                                    // 通知下载完成
                                    CoroutineScope(Dispatchers.Main).launch {
                                        val result = mutableMapOf<String, Any>()
                                        result["status"] = "completed"
                                        result["progress"] = 100
                                        result["filePath"] = file.absolutePath
                                        updateCallback?.invoke(result)

                                        // 延迟一秒后安装APK，确保UI更新完成
                                        withContext(Dispatchers.IO) {
                                            try {
                                                Thread.sleep(1000)
                                            } catch (e: InterruptedException) {
                                                // 忽略中断异常
                                            }
                                        }

                                        // 安装APK
                                        Log.d(TAG, "广播接收器：准备安装APK")
                                        installApk(file)
                                    }
                                } else {
                                    Log.e(TAG, "广播接收器：文件不存在: ${file?.absolutePath}")
                                    CoroutineScope(Dispatchers.Main).launch {
                                        val result = mutableMapOf<String, Any>()
                                        result["status"] = "error"
                                        result["error"] = "下载文件不存在: ${file?.absolutePath}"
                                        updateCallback?.invoke(result)
                                    }
                                }
                            }
                            DownloadManager.STATUS_FAILED -> {
                                val reasonIndex = cursor.getColumnIndex(DownloadManager.COLUMN_REASON)
                                val reason = cursor.getInt(reasonIndex)
                                CoroutineScope(Dispatchers.Main).launch {
                                    val result = mutableMapOf<String, Any>()
                                    result["status"] = "error"
                                    result["error"] = "下载失败，错误代码: $reason"
                                    updateCallback?.invoke(result)
                                }
                            }
                        }
                    }
                    cursor.close()
                }
            }
        }

        // 注册接收器
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+需要明确指定exported属性
            context.registerReceiver(
                downloadReceiver,
                IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE),
                Context.RECEIVER_NOT_EXPORTED
            )
        } else {
            // 旧版本Android使用旧的API
            context.registerReceiver(
                downloadReceiver,
                IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE)
            )
        }
    }

    /**
     * 取消注册下载完成广播接收器
     */
    private fun unregisterDownloadReceiver() {
        downloadReceiver?.let {
            try {
                context.unregisterReceiver(it)
            } catch (e: Exception) {
                Log.e(TAG, "取消注册下载接收器失败", e)
            }
            downloadReceiver = null
        }
    }

    /**
     * 监控下载进度
     * @param downloadManager 下载管理器
     * @param downloadId 下载ID
     * @param callback 回调函数
     */
    private fun monitorDownloadProgress(
        downloadManager: DownloadManager,
        downloadId: Long,
        callback: (Map<String, Any>) -> Unit
    ) {
        executor.execute {
            var isDownloading = true
            var lastProgress = -1
            var errorCount = 0
            var lastActivityTime = System.currentTimeMillis()
            val maxInactivityTime = 30000L // 30秒无活动视为下载卡住

            Log.d(TAG, "开始监控下载进度，downloadId=$downloadId")

            while (isDownloading && downloadId != -1L) {
                try {
                    val query = DownloadManager.Query().setFilterById(downloadId)
                    val cursor = downloadManager.query(query)

                    if (cursor.moveToFirst()) {
                        val statusIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS)
                        val bytesDownloadedIndex = cursor.getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR)
                        val bytesTotalIndex = cursor.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES)
                        val reasonIndex = cursor.getColumnIndex(DownloadManager.COLUMN_REASON)

                        val status = cursor.getInt(statusIndex)
                        val bytesDownloaded = cursor.getLong(bytesDownloadedIndex)
                        val bytesTotal = cursor.getLong(bytesTotalIndex)
                        val reason = cursor.getInt(reasonIndex)

                        Log.d(TAG, "下载状态: $status, 已下载: $bytesDownloaded, 总大小: $bytesTotal, 原因: $reason")

                        when (status) {
                            DownloadManager.STATUS_RUNNING -> {
                                // 更新最后活动时间
                                lastActivityTime = System.currentTimeMillis()

                                if (bytesTotal > 0) {
                                    val progress = (bytesDownloaded * 100 / bytesTotal).toInt()
                                    if (progress != lastProgress) {
                                        lastProgress = progress

                                        // 在主线程中回调
                                        CoroutineScope(Dispatchers.Main).launch {
                                            val result = mutableMapOf<String, Any>()
                                            result["status"] = "downloading"
                                            result["progress"] = progress
                                            callback(result)
                                        }
                                    }
                                }
                            }
                            DownloadManager.STATUS_SUCCESSFUL -> {
                                Log.d(TAG, "下载成功，准备安装")
                                isDownloading = false

                                // 获取下载的文件
                                val localUriIndex = cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI)
                                if (localUriIndex != -1) {
                                    val localUri = cursor.getString(localUriIndex)
                                    Log.d(TAG, "下载文件URI: $localUri")

                                    if (localUri != null) {
                                        val fileUri = Uri.parse(localUri)
                                        val path = if (fileUri.scheme == "file") {
                                            fileUri.path
                                        } else {
                                            // 从content URI获取文件路径
                                            val downloadedFile = File(
                                                context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS),
                                                "novel_app_${fileUri.lastPathSegment?.substringAfterLast('/')?.substringAfterLast('_') ?: ""}.apk"
                                            )
                                            downloadedFile.absolutePath
                                        }

                                        Log.d(TAG, "下载文件路径: $path")
                                        val file = path?.let { File(it) }
                                        if (file != null && file.exists()) {
                                            Log.d(TAG, "文件存在，大小: ${file.length()} 字节")
                                            // 通知下载完成
                                            CoroutineScope(Dispatchers.Main).launch {
                                                val result = mutableMapOf<String, Any>()
                                                result["status"] = "completed"
                                                result["progress"] = 100
                                                result["filePath"] = file.absolutePath
                                                callback(result)

                                                // 安装APK
                                                installApk(file)
                                            }
                                        } else {
                                            Log.e(TAG, "文件不存在: ${file?.absolutePath}")
                                        }
                                    } else {
                                        Log.e(TAG, "本地URI为null")
                                    }
                                } else {
                                    Log.e(TAG, "找不到COLUMN_LOCAL_URI列")
                                }
                            }
                            DownloadManager.STATUS_FAILED -> {
                                Log.e(TAG, "下载失败，原因代码: $reason")
                                isDownloading = false

                                // 报告错误
                                CoroutineScope(Dispatchers.Main).launch {
                                    val result = mutableMapOf<String, Any>()
                                    result["status"] = "error"
                                    result["error"] = "下载失败，错误代码: $reason"
                                    callback(result)
                                }
                            }
                            DownloadManager.STATUS_PAUSED -> {
                                Log.w(TAG, "下载暂停，原因代码: $reason")

                                // 检查是否暂停时间过长
                                val currentTime = System.currentTimeMillis()
                                if (currentTime - lastActivityTime > maxInactivityTime) {
                                    Log.w(TAG, "下载暂停时间过长，尝试恢复")

                                    // 尝试恢复下载
                                    try {
                                        // 在主线程中通知用户
                                        CoroutineScope(Dispatchers.Main).launch {
                                            val result = mutableMapOf<String, Any>()
                                            result["status"] = "downloading"
                                            result["progress"] = lastProgress
                                            result["message"] = "下载暂停，正在尝试恢复..."
                                            callback(result)

                                            // 显示Toast提示
                                            Toast.makeText(context, "下载暂停，正在尝试恢复...", Toast.LENGTH_SHORT).show()
                                        }

                                        // 更新最后活动时间，避免频繁重试
                                        lastActivityTime = currentTime
                                    } catch (e: Exception) {
                                        Log.e(TAG, "尝试恢复下载失败", e)
                                    }
                                }
                            }
                        }
                    } else {
                        Log.w(TAG, "查询下载状态失败，cursor为空")
                        isDownloading = false
                    }

                    cursor.close()

                    // 检查下载是否卡住（进度长时间不变）
                    val currentTime = System.currentTimeMillis()
                    if (lastProgress > 0 && lastProgress < 100 && currentTime - lastActivityTime > maxInactivityTime) {
                        Log.w(TAG, "下载似乎卡住了，已经${(currentTime - lastActivityTime) / 1000}秒没有进度更新")

                        // 在主线程中通知用户
                        CoroutineScope(Dispatchers.Main).launch {
                            val result = mutableMapOf<String, Any>()
                            result["status"] = "downloading"
                            result["progress"] = lastProgress
                            result["message"] = "下载速度较慢，请耐心等待..."
                            callback(result)

                            // 显示Toast提示
                            Toast.makeText(context, "下载速度较慢，请耐心等待...", Toast.LENGTH_SHORT).show()
                        }

                        // 更新最后活动时间，避免频繁提示
                        lastActivityTime = currentTime
                    }

                    try {
                        // 每500毫秒检查一次进度
                        Thread.sleep(500)
                    } catch (e: InterruptedException) {
                        Log.w(TAG, "监控线程被中断", e)
                        isDownloading = false
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "监控下载进度失败", e)
                    errorCount++

                    if (errorCount > 5) {
                        // 连续错误次数过多，停止监控
                        isDownloading = false

                        // 报告错误
                        CoroutineScope(Dispatchers.Main).launch {
                            val result = mutableMapOf<String, Any>()
                            result["status"] = "error"
                            result["error"] = "监控下载进度失败: ${e.message}"
                            callback(result)
                        }
                    } else {
                        // 短暂延迟后继续尝试
                        try {
                            Thread.sleep(1000)
                        } catch (ie: InterruptedException) {
                            isDownloading = false
                        }
                    }
                }
            }

            Log.d(TAG, "下载监控结束，downloadId=$downloadId")
        }
    }
}
