import 'package:flutter/foundation.dart';

class ModelConfig {
  String name; // 模型名称
  String apiKey; // API密钥
  String apiUrl; // API地址
  String apiPath; // API路径
  String model; // 具体模型名称
  List<String> modelVariants; // 模型变体列表（多模型标识符）
  String apiFormat; // API格式（如OpenAI API兼容等）
  String appId; // 应用ID（百度千帆等需要）
  bool isCustom; // 是否为自定义模型
  double temperature;
  double topP;
  int maxTokens;
  double repetitionPenalty; // 重复惩罚参数
  bool useProxy; // 是否使用代理
  String proxyUrl; // 代理服务器地址
  int timeout; // 请求超时时间（秒）
  bool enableThinking; // 是否启用深度思考模式（阿里云通义千问特有）

  ModelConfig({
    required this.name,
    required this.apiKey,
    required this.apiUrl,
    required this.apiPath,
    required this.model,
    this.modelVariants = const [],
    required this.apiFormat,
    this.appId = '',
    this.isCustom = false,
    this.temperature = 0.7,
    this.topP = 1.0,
    this.maxTokens = 6000,
    this.repetitionPenalty = 1.3, // 设置默认值
    this.useProxy = false, // 默认不使用代理
    this.proxyUrl = '', // 默认代理地址为空
    this.timeout = 60, // 默认超时时间为60秒
    this.enableThinking = false, // 默认不启用深度思考模式
  });

  Map<String, dynamic> toJson() => {
        'name': name,
        'apiKey': apiKey,
        'apiUrl': apiUrl,
        'apiPath': apiPath,
        'model': model,
        'modelVariants': modelVariants,
        'apiFormat': apiFormat,
        'appId': appId,
        'isCustom': isCustom,
        'temperature': temperature,
        'topP': topP,
        'maxTokens': maxTokens,
        'repetitionPenalty': repetitionPenalty, // 添加到 JSON
        'useProxy': useProxy, // 添加代理设置
        'proxyUrl': proxyUrl, // 添加代理URL
        'timeout': timeout, // 添加超时时间
        'enableThinking': enableThinking, // 添加深度思考模式
      };

  factory ModelConfig.fromJson(Map<String, dynamic> json) => ModelConfig(
        name: json['name'] as String? ?? '',
        apiKey: json['apiKey'] as String? ?? '',
        apiUrl: json['apiUrl'] as String? ?? '',
        apiPath: json['apiPath'] as String? ?? '',
        model: json['model'] as String? ?? '',
        modelVariants: json['modelVariants'] != null
            ? List<String>.from(json['modelVariants'])
            : [],
        apiFormat: json['apiFormat'] as String? ?? 'OpenAI API兼容',
        appId: json['appId'] as String? ?? '',
        isCustom: json['isCustom'] as bool? ?? false,
        temperature: (json['temperature'] as num?)?.toDouble() ?? 0.7,
        topP: (json['topP'] as num?)?.toDouble() ?? 1.0,
        maxTokens: (json['maxTokens'] as num?)?.toInt() ?? 4000,
        repetitionPenalty:
            (json['repetitionPenalty'] as num?)?.toDouble() ?? 1.3, // 从 JSON 读取
        useProxy: json['useProxy'] as bool? ?? false, // 从 JSON 读取代理设置
        proxyUrl: json['proxyUrl'] as String? ?? '', // 从 JSON 读取代理URL
        timeout: (json['timeout'] as num?)?.toInt() ?? 60, // 从 JSON 读取超时时间
        enableThinking:
            json['enableThinking'] as bool? ?? false, // 从 JSON 读取深度思考模式
      );

  ModelConfig copyWith({
    String? name,
    String? apiKey,
    String? apiUrl,
    String? apiPath,
    String? model,
    List<String>? modelVariants,
    String? apiFormat,
    String? appId,
    bool? isCustom,
    double? temperature,
    double? topP,
    int? maxTokens,
    double? repetitionPenalty, // 添加到 copyWith
    bool? useProxy, // 添加代理设置
    String? proxyUrl, // 添加代理URL
    int? timeout, // 添加超时时间
    bool? enableThinking, // 添加深度思考模式
  }) {
    return ModelConfig(
      name: name ?? this.name,
      apiKey: apiKey ?? this.apiKey,
      apiUrl: apiUrl ?? this.apiUrl,
      apiPath: apiPath ?? this.apiPath,
      model: model ?? this.model,
      modelVariants: modelVariants ?? this.modelVariants,
      apiFormat: apiFormat ?? this.apiFormat,
      appId: appId ?? this.appId,
      isCustom: isCustom ?? this.isCustom,
      temperature: temperature ?? this.temperature,
      topP: topP ?? this.topP,
      maxTokens: maxTokens ?? this.maxTokens,
      repetitionPenalty: repetitionPenalty ?? this.repetitionPenalty, // 添加到构造
      useProxy: useProxy ?? this.useProxy, // 添加代理设置
      proxyUrl: proxyUrl ?? this.proxyUrl, // 添加代理URL
      timeout: timeout ?? this.timeout, // 添加超时时间
      enableThinking: enableThinking ?? this.enableThinking, // 添加深度思考模式
    );
  }

  // 添加模型变体
  void addModelVariant(String variant) {
    if (!modelVariants.contains(variant) && variant.isNotEmpty) {
      modelVariants.add(variant);
    }
  }

  // 删除模型变体
  void removeModelVariant(String variant) {
    modelVariants.remove(variant);
  }

  // 切换当前模型为指定变体
  void switchToVariant(String variant) {
    if (modelVariants.contains(variant)) {
      model = variant;
    }
  }
}
