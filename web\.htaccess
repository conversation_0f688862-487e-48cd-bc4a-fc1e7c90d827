# 启用重写引擎
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    
    # 如果请求的是实际文件或目录，则直接访问
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    
    # 否则将请求重定向到index.html
    RewriteRule . /index.html [L]
</IfModule>

# 设置正确的MIME类型
AddType application/javascript .js
AddType application/json .json
AddType application/wasm .wasm

# 启用CORS
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
</IfModule>

# 禁用目录列表
Options -Indexes

# 启用缓存，但对flutter.js禁用缓存以确保始终获取最新版本
<IfModule mod_expires.c>
    ExpiresActive On
    
    # 对flutter.js禁用缓存
    <FilesMatch "flutter\.js$">
        ExpiresDefault "access"
        Header set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
        Header set Pragma "no-cache"
    </FilesMatch>
    
    # 对其他资源启用长期缓存
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType application/wasm "access plus 1 year"
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>
