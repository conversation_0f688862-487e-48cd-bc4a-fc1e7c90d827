from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, <PERSON><PERSON>ey, Integer, String, Text, func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
import uuid
from datetime import datetime, timedelta

Base = declarative_base()

class User(Base):
    """用户模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    is_vip = Column(Boolean, default=False)
    is_admin = Column(Boolean, default=False)
    vip_expire_time = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系
    novels = relationship("Novel", back_populates="user")

class Novel(Base):
    """小说模型"""
    __tablename__ = "novels"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String(100), nullable=False)
    genre = Column(String(50), nullable=False)
    outline = Column(Text, nullable=True)
    content = Column(Text, nullable=True)
    style = Column(String(50), nullable=True)
    session_id = Column(String(36), nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 外键
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # 关系
    user = relationship("User", back_populates="novels")
    chapters = relationship("Chapter", back_populates="novel", cascade="all, delete-orphan")

class Chapter(Base):
    """章节模型"""
    __tablename__ = "chapters"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    number = Column(Integer, nullable=False)
    title = Column(String(100), nullable=False)
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=func.now())

    # 外键
    novel_id = Column(String(36), ForeignKey("novels.id"), nullable=False)

    # 关系
    novel = relationship("Novel", back_populates="chapters")

class Announcement(Base):
    """公告模型"""
    __tablename__ = "announcements"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String(100), nullable=False)
    content = Column(Text, nullable=False)
    is_important = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
