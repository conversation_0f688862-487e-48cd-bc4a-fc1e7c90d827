import 'package:get/get.dart';
import 'package:novel_app/models/writing_style_package.dart';
import 'package:hive_flutter/hive_flutter.dart';

class WritingStylePackageController extends GetxController {
  final RxList<WritingStylePackage> packages = <WritingStylePackage>[].obs;
  final Rx<WritingStylePackage?> selectedPackage =
      Rx<WritingStylePackage?>(null);

  // 当前使用的文风包
  Rx<WritingStylePackage?> get currentPackage => selectedPackage;

  static const String _boxName = 'writing_style_packages';
  late final Box<dynamic> _box;

  @override
  void onInit() async {
    super.onInit();
    await _initHive();
    await loadPackages();
  }

  Future<void> _initHive() async {
    _box = await Hive.openBox(_boxName);
  }

  Future<void> loadPackages() async {
    try {
      final savedPackages = _box.get('packages');
      if (savedPackages != null) {
        if (savedPackages is List) {
          packages.value = savedPackages
              .map((packageData) => packageData is WritingStylePackage
                  ? packageData
                  : WritingStylePackage.fromJson(
                      Map<String, dynamic>.from(packageData)))
              .toList();
        }
      }
      print('加载文风包成功: ${packages.length} 个');
    } catch (e) {
      print('加载文风包失败: $e');
      packages.clear();
    }
  }

  Future<void> addPackage(WritingStylePackage package) async {
    try {
      packages.add(package);
      await _savePackages();
      print('添加文风包成功: ${package.name}');
    } catch (e) {
      print('添加文风包失败: $e');
      rethrow;
    }
  }

  Future<void> removePackage(String id) async {
    try {
      packages.removeWhere((package) => package.id == id);
      await _savePackages();
      print('删除文风包成功: $id');
    } catch (e) {
      print('删除文风包失败: $e');
      rethrow;
    }
  }

  Future<void> updatePackage(WritingStylePackage package) async {
    try {
      final index = packages.indexWhere((p) => p.id == package.id);
      if (index != -1) {
        packages[index] = package;
        await _savePackages();
        print('更新文风包成功: ${package.name}');
      }
    } catch (e) {
      print('更新文风包失败: $e');
      rethrow;
    }
  }

  Future<void> _savePackages() async {
    try {
      await _box.put('packages', packages);
      print('保存文风包成功');
    } catch (e) {
      print('保存文风包失败: $e');
      rethrow;
    }
  }

  void selectPackage(String id) {
    selectedPackage.value = packages.firstWhere((package) => package.id == id);
  }

  void clearSelectedPackage() {
    selectedPackage.value = null;
  }

  // 导入文风包
  Future<void> importPackages(
      List<WritingStylePackage> importedPackages) async {
    try {
      for (final package in importedPackages) {
        // 检查是否已存在相同ID的文风包
        final existingIndex = packages.indexWhere((p) => p.id == package.id);
        if (existingIndex != -1) {
          // 如果存在，更新它
          packages[existingIndex] = package;
        } else {
          // 如果不存在，添加它
          packages.add(package);
        }
      }
      await _savePackages();
      print('导入文风包成功: ${importedPackages.length} 个');
    } catch (e) {
      print('导入文风包失败: $e');
      rethrow;
    }
  }

  // 导出文风包
  List<WritingStylePackage> exportPackages(List<String> packageIds) {
    return packages
        .where((package) => packageIds.contains(package.id))
        .toList();
  }
}
