@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo Checking Environment for Novel App Build
echo ===================================================

set PROJECT_DIR=D:\project\vs code\novel_app
set FLUTTER_PATH=D:\element\flutter\bin
set INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 6
set VC_REDIST_PATH=%PROJECT_DIR%\windows\installer\VC_redist.x64.exe

echo Checking project directory...
if not exist "%PROJECT_DIR%" (
    echo ERROR: Project directory not found: %PROJECT_DIR%
    exit /b 1
) else (
    echo Project directory found: %PROJECT_DIR%
)

echo.
echo Checking Flutter installation...
if not exist "%FLUTTER_PATH%\flutter.bat" (
    echo ERROR: Flutter not found at: %FLUTTER_PATH%
    exit /b 1
) else (
    echo Flutter found at: %FLUTTER_PATH%
    call %FLUTTER_PATH%\flutter --version | findstr "Flutter"
)

echo.
echo Checking Inno Setup installation...
if not exist "%INNO_SETUP_PATH%\ISCC.exe" (
    echo ERROR: Inno Setup not found at: %INNO_SETUP_PATH%
    echo Please install Inno Setup from: https://jrsoftware.org/isdl.php
    exit /b 1
) else (
    echo Inno Setup found at: %INNO_SETUP_PATH%
)

echo.
echo Checking VC++ Redistributable installer...
if not exist "%VC_REDIST_PATH%" (
    echo WARNING: VC++ Redistributable installer not found at: %VC_REDIST_PATH%
    echo Please download it from: https://aka.ms/vs/17/release/vc_redist.x64.exe
    echo And place it in: %PROJECT_DIR%\windows\installer\
    
    echo Creating installer directory if it doesn't exist...
    if not exist "%PROJECT_DIR%\windows\installer" mkdir "%PROJECT_DIR%\windows\installer"
) else (
    echo VC++ Redistributable installer found at: %VC_REDIST_PATH%
)

echo.
echo Checking pubspec.yaml...
if not exist "%PROJECT_DIR%\pubspec.yaml" (
    echo ERROR: pubspec.yaml not found at: %PROJECT_DIR%\pubspec.yaml
    exit /b 1
) else (
    echo pubspec.yaml found at: %PROJECT_DIR%\pubspec.yaml
    echo Current version:
    type "%PROJECT_DIR%\pubspec.yaml" | findstr "version:"
)

echo.
echo Checking Windows build configuration...
if not exist "%PROJECT_DIR%\windows\CMakeLists.txt" (
    echo ERROR: Windows build configuration not found
    exit /b 1
) else (
    echo Windows build configuration found
)

echo.
echo ===================================================
echo Environment check completed!
echo ===================================================
echo.
echo If all checks passed, you can run build_windows_4.2.5.1.bat to build the Windows package.
echo.

endlocal
