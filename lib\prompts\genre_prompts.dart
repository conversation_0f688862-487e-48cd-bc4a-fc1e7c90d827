// 类型提示词文件
// 包含各种小说类型的特定要求和提示词
// 在 GenreController 和 NovelGeneratorService 中使用

class NovelGenre {
  final String name;
  final String description;
  final String prompt;

  const NovelGenre({
    required this.name,
    required this.description,
    required this.prompt,
  });
}

class GenreCategory {
  final String name;
  final List<NovelGenre> genres;

  const GenreCategory({
    required this.name,
    required this.genres,
  });
}

class GenrePrompts {
  static const List<GenreCategory> categories = [
    GenreCategory(
      name: '都市现代',
      genres: [
        NovelGenre(
          name: '都市异能',
          description: '都市背景下的超能力故事',
          prompt: '都市异能：重生、系统流、赘婿、神豪等元素',
        ),
        NovelGenre(
          name: '娱乐圈',
          description: '演艺圈发展故事',
          prompt: '娱乐圈：星探、试镜、爆红、黑料等元素',
        ),
        NovelGenre(
          name: '职场商战',
          description: '职场或商业竞争故事',
          prompt: '职场商战：升职加薪、商业谈判、公司运营等元素',
        ),
        NovelGenre(
          name: '亿万富翁',
          description: '富豪人生故事',
          prompt: '亿万富翁：财富积累、商业帝国、豪门生活等元素',
        ),
        NovelGenre(
          name: '赘婿',
          description: '赘婿逆袭的故事',
          prompt: '赘婿：豪门婚姻、家族斗争、身份认同等元素',
        ),
        NovelGenre(
          name: '龙王',
          description: '怎么可能，你是龙王！！！',
          prompt: '龙王：龙族传说、血脉觉醒、异界探险等元素',
        ),
      ],
    ),
    GenreCategory(
      name: '玄幻修仙',
      genres: [
        NovelGenre(
          name: '玄幻修仙',
          description: '修真问道的故事',
          prompt: '玄幻修仙：修炼体系、宗门势力、天材地宝等元素',
        ),
        NovelGenre(
          name: '重生',
          description: '重获新生的故事',
          prompt: '重生：前世记忆、改变命运、复仇崛起等元素',
        ),
        NovelGenre(
          name: '系统流',
          description: '获得系统的故事',
          prompt: '系统流：金手指、任务奖励、属性面板等元素',
        ),
      ],
    ),
    GenreCategory(
      name: '游戏竞技',
      genres: [
        NovelGenre(
          name: '电竞',
          description: '电子竞技故事',
          prompt: '电竞：职业选手、战队训练、比赛竞技等元素',
        ),
        NovelGenre(
          name: '游戏',
          description: '游戏世界的故事',
          prompt: '游戏：虚拟世界、副本攻略、公会组织等元素',
        ),
        NovelGenre(
          name: '无限流',
          description: '轮回闯关的故事',
          prompt: '无限流：任务世界、轮回闯关、积分兑换等元素',
        ),
      ],
    ),
    GenreCategory(
      name: '科幻未来',
      genres: [
        NovelGenre(
          name: '末世',
          description: '末日求生的故事',
          prompt: '末世：病毒爆发、丧尸横行、废土重建等元素',
        ),
        NovelGenre(
          name: '赛博朋克',
          description: '高科技低生活的故事',
          prompt: '赛博朋克：机械改造、黑客技术、巨型企业等元素',
        ),
        NovelGenre(
          name: '机器人觉醒',
          description: 'AI觉醒的故事',
          prompt: '机器人觉醒：人工智能、机械文明、人机共存等元素',
        ),
        NovelGenre(
          name: '星际',
          description: '在遥远的未来---',
          prompt: '星际：星际航行、外星文明、科技发达等元素',
        ),
      ],
    ),
    GenreCategory(
      name: '古代历史',
      genres: [
        NovelGenre(
          name: '宫斗',
          description: '后宫争斗的故事',
          prompt: '宫斗：后宫争宠、权谋算计、皇权斗争等元素',
        ),
        NovelGenre(
          name: '穿越',
          description: '穿越时空的故事',
          prompt: '穿越：时空穿梭、历史改变、文化冲突等元素',
        ),
        NovelGenre(
          name: '种田',
          description: '农家生活的故事',
          prompt: '种田：农家生活、乡村发展、生活技能等元素',
        ),
        NovelGenre(
          name: '民国',
          description: '民国时期的故事',
          prompt: '民国：乱世生存、谍战情报、革命斗争等元素',
        ),
        NovelGenre(
          name: '抗战军事',
          description: '抗战时期的军事故事',
          prompt: '抗战军事：抗日战争、军旅生活、战略战术等元素',
        ),
        NovelGenre(
          name: '年代文',
          description: '架空历史的故事',
          prompt: '年代文：知青岁月、工厂风云、票证生活、高考恢复、下海经商等元素',
        ),
      ],
    ),
    GenreCategory(
      name: '情感',
      genres: [
        NovelGenre(
          name: '言情',
          description: '纯爱故事',
          prompt: '言情：甜宠恋爱、情感纠葛、浪漫邂逅等元素',
        ),
        NovelGenre(
          name: '虐文',
          description: '虐心故事',
          prompt: '虐文：情感折磨、误会纠葛、痛苦救赎等元素',
        ),
        NovelGenre(
          name: '禁忌之恋',
          description: '禁忌感情故事',
          prompt: '禁忌之恋：身份差距、伦理冲突、命运阻隔等元素',
        ),
        NovelGenre(
          name: '耽美',
          description: '男男感情故事',
          prompt: '耽美：男男情感、相知相守、甜虐交织等元素',
        ),
        NovelGenre(
          name: '后宫',
          description: '女女感情故事',
          prompt: '后宫文：一名男性主角与多名女性角色之间存在暧昧或恋爱关系，并围绕着这些情感纠葛展开故事情节',
        ),
      ],
    ),
    GenreCategory(name: '角色特设', genres: [
      NovelGenre(
        name: 'ABO',
        description: '特殊三角色',
        prompt:
            '赘婿：将人物分为Alpha（阿尔法）、Beta（贝塔）、Omega（欧米伽）三种性别，通常Alpha体力强、具领导气质，Omega较为柔弱且能生育，Beta则处于两者之间，这种设定常涉及到一些特殊的生理特征、社会地位和情感关系等',
      ),
      NovelGenre(
        name: '双女主',
        description: '两个女主角之间的爱情火花',
        prompt: '双女主：两个女主角之间的爱情火花、情感纠葛等元素',
      ),
      NovelGenre(
        name: '双男主',
        description: '两个男主角之间的友情与爱情',
        prompt: '双男主：两个男主角之间的友情与爱情等元素',
      ),
      NovelGenre(
        name: '主仆恋',
        description: '主人与仆人之间的爱情',
        prompt: '主仆恋：主人与仆人之间的爱情、身份差异等元素',
      ),
      NovelGenre(
        name: '师生恋',
        description: '师生之间的爱情',
        prompt: '师生恋：师生之间的爱情、身份差异等元素',
      ),
      NovelGenre(
        name: '姐弟恋',
        description: '姐弟之间的爱情',
        prompt: '姐弟恋：姐弟之间的爱情、身份差异等元素',
      ),
      NovelGenre(
        name: '兄妹恋',
        description: '兄妹之间的爱情',
        prompt: '兄妹恋：兄妹之间的爱情、身份差异等元素',
      ),
      NovelGenre(
        name: '父女恋',
        description: '父女之间的爱情',
        prompt: '父女恋：父女之间的爱情、身份差异等元素',
      ),
      NovelGenre(
        name: '母子恋',
        description: '母子之间的爱情',
        prompt: '母子恋：母子之间的爱情、身份差异等元素',
      ),
      NovelGenre(
        name: '兽人',
        description: '',
        prompt: '兽人：兽人族群冲突、异能战斗、兽性本能与情感纠葛',
      ),
    ]),
    GenreCategory(
      name: '其他题材',
      genres: [
        NovelGenre(
          name: '灵异',
          description: '灵异故事',
          prompt: '灵异：鬼怪神秘、通灵驱邪、阴阳交界等元素',
        ),
        NovelGenre(
          name: '悬疑',
          description: '悬疑推理故事',
          prompt: '悬疑：案件侦破、推理解谜、心理较量等元素',
        ),
        NovelGenre(
          name: '沙雕',
          description: '搞笑欢乐故事',
          prompt: '沙雕：欢乐搞笑、日常吐槽、轻松愉快等元素',
        ),
        NovelGenre(
          name: '直播',
          description: '直播生活故事',
          prompt: '直播：网络主播、粉丝互动、直播生态等元素',
        ),
        NovelGenre(
          name: '克苏鲁',
          description: '克苏鲁世界观的故事',
          prompt:
              '克苏鲁：克苏鲁世界观是一种以恐怖、悬疑、神秘为主要风格的文学类型。其特点是强调宇宙的神秘和未知，存在着远超人类理解的强大邪恶存在，如克苏鲁等旧日支配者。故事氛围通常阴暗压抑，充满绝望感，人类在面对这些超自然力量时往往显得渺小无助，常伴有对疯狂、禁忌知识的探索及怪诞恐怖的描写。',
        ),
        NovelGenre(
          name: '我的七个姐姐绝色倾城',
          description: '我和七个姐姐的故事',
          prompt:
              '我和七个姐姐的故事，我和七个姐姐从小在孤儿院长大，但是分别被领养之后失去了联系，我是化境宗师，宗门中的最强者，但是我一直刻意低调在这个到处是世家大族的城市，后来我逐个和她们取得了联系……',
        ),
      ],
    ),
  ];

  /// 根据小说类型获取提示词
  static String getPromptByGenre(String genre) {
    for (var category in categories) {
      for (var novelGenre in category.genres) {
        if (novelGenre.name == genre) {
          return novelGenre.prompt;
        }
      }
    }
    // 如果找不到对应类型，返回都市异能的提示词作为默认值
    return categories[0].genres[0].prompt;
  }
}
