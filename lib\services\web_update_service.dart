import 'dart:convert';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/material.dart';

/// Web平台版本信息模型
class WebVersionInfo {
  final String version;
  final String buildNumber;
  final String releaseDate;
  final String downloadUrl;
  final String releaseNotes;
  final bool forceUpdate;

  WebVersionInfo({
    required this.version,
    required this.buildNumber,
    required this.downloadUrl,
    required this.releaseNotes,
    this.releaseDate = '',
    this.forceUpdate = false,
  });

  factory WebVersionInfo.fromJson(Map<String, dynamic> json) {
    return WebVersionInfo(
      version: json['version'] ?? '',
      buildNumber: json['buildNumber'] ?? '',
      releaseDate: json['releaseDate'] ?? '',
      downloadUrl: json['downloadUrl'] ?? '',
      releaseNotes: json['releaseNotes'] ?? '',
      forceUpdate: json['forceUpdate'] ?? false,
    );
  }

  /// 检查是否比指定版本更新
  bool isNewerThan(String otherVersion, String otherBuildNumber) {
    // 解析版本号
    final List<int> thisVersion = version.split('.')
        .map((part) => int.tryParse(part) ?? 0)
        .toList();
    final List<int> compareVersion = otherVersion.split('.')
        .map((part) => int.tryParse(part) ?? 0)
        .toList();

    // 确保两个版本号列表长度相同
    while (thisVersion.length < compareVersion.length) {
      thisVersion.add(0);
    }
    while (compareVersion.length < thisVersion.length) {
      compareVersion.add(0);
    }

    // 比较版本号
    for (int i = 0; i < thisVersion.length; i++) {
      if (thisVersion[i] > compareVersion[i]) {
        return true;
      } else if (thisVersion[i] < compareVersion[i]) {
        return false;
      }
    }

    // 如果版本号相同，比较构建号
    final int thisBuild = int.tryParse(buildNumber) ?? 0;
    final int compareBuild = int.tryParse(otherBuildNumber) ?? 0;
    return thisBuild > compareBuild;
  }
}

/// Web平台更新服务
class WebUpdateService extends GetxService {
  static const String _lastCheckTimeKey = 'last_update_check_time';
  static const String _ignoreVersionKey = 'ignore_update_version';
  static const Duration _checkInterval = Duration(hours: 12); // 检查间隔时间

  final updateServerUrl = 'https://dzwm.xyz/api/version.json'; // 主要更新服务器URL
  final backupUpdateServerUrl = 'http://47.120.19.139/api/version.json'; // 备用更新服务器URL
  
  final Rxn<WebVersionInfo> latestVersion = Rxn<WebVersionInfo>();
  final RxBool isCheckingUpdate = false.obs;
  final RxBool updateAvailable = false.obs;

  late final SharedPreferences _prefs;

  @override
  void onInit() {
    super.onInit();
    _initPrefs();
  }

  Future<void> _initPrefs() async {
    _prefs = await SharedPreferences.getInstance();
    // 应用启动时自动检查更新
    checkForUpdates(showLoading: false);
  }

  /// 检查更新
  Future<void> checkForUpdates({
    bool showLoading = true,
    bool forceCheck = false,
  }) async {
    try {
      // 如果正在检查，直接返回
      if (isCheckingUpdate.value) return;

      isCheckingUpdate.value = true;
      if (showLoading) {
        Get.dialog(
          const Center(child: CircularProgressIndicator()),
          barrierDismissible: false,
        );
      }

      // 检查是否需要检查更新
      if (!forceCheck && !_shouldCheckForUpdates()) {
        isCheckingUpdate.value = false;
        if (showLoading) Get.back();
        return;
      }

      // 获取当前应用版本信息 (Web版本无法获取，使用固定值)
      final currentVersion = '1.0.0';
      final currentBuildNumber = '1';

      // 获取最新版本信息
      WebVersionInfo newVersion;

      try {
        // 从服务器获取版本信息
        try {
          // 首先尝试主要URL
          final response = await http
              .get(Uri.parse(updateServerUrl))
              .timeout(const Duration(seconds: 10));

          if (response.statusCode == 200) {
            final Map<String, dynamic> data = json.decode(response.body);
            newVersion = WebVersionInfo.fromJson(data);
          } else {
            throw Exception('主服务器响应错误: ${response.statusCode}');
          }
        } catch (e) {
          print('主服务器请求失败，尝试备用服务器: $e');

          // 如果主要URL失败，尝试备用URL
          final response = await http
              .get(Uri.parse(backupUpdateServerUrl))
              .timeout(const Duration(seconds: 10));

          if (response.statusCode == 200) {
            final Map<String, dynamic> data = json.decode(response.body);
            newVersion = WebVersionInfo.fromJson(data);
          } else {
            throw Exception('备用服务器响应错误: ${response.statusCode}');
          }
        }

        // 保存最新版本信息
        latestVersion.value = newVersion;

        // 保存最后检查时间
        _prefs.setInt(_lastCheckTimeKey, DateTime.now().millisecondsSinceEpoch);

        // 检查是否有新版本
        final String ignoredVersion = _prefs.getString(_ignoreVersionKey) ?? '';
        final bool isIgnored = ignoredVersion == newVersion.version;

        if (newVersion.isNewerThan(currentVersion, currentBuildNumber) &&
            !isIgnored) {
          updateAvailable.value = true;

          // 如果显示加载对话框，先关闭它
          if (showLoading) Get.back();

          // 显示更新对话框
          _showUpdateDialog(newVersion);
        } else {
          updateAvailable.value = false;
          if (showLoading) {
            Get.back();
            Get.snackbar('检查更新', '您当前使用的已经是最新版本');
          }
        }
      } catch (error) {
        if (showLoading) {
          Get.back();
          Get.snackbar('检查更新失败', error.toString());
        }
        rethrow;
      }
    } catch (e) {
      if (showLoading) {
        Get.back();
        Get.snackbar('检查更新失败', '请检查网络连接: $e');
      }
    } finally {
      isCheckingUpdate.value = false;
    }
  }

  /// 显示更新对话框
  void _showUpdateDialog(WebVersionInfo version) {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.system_update, color: Colors.blue),
            SizedBox(width: 8),
            Text('发现新版本'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('最新版本: ${version.version}'),
              if (version.releaseDate.isNotEmpty)
                Text('发布日期: ${version.releaseDate}'),
              const SizedBox(height: 16),
              const Text('更新内容:'),
              Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(version.releaseNotes),
              ),
            ],
          ),
        ),
        actions: [
          if (!version.forceUpdate)
            TextButton(
              onPressed: () {
                // 忽略此版本
                _prefs.setString(_ignoreVersionKey, version.version);
                Get.back();
              },
              child: const Text('忽略此版本'),
            ),
          if (!version.forceUpdate)
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('稍后更新'),
            ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _openDownloadUrl(version.downloadUrl);
            },
            child: const Text('立即更新'),
          ),
        ],
      ),
      barrierDismissible: !version.forceUpdate,
    );
  }

  /// 打开下载链接
  Future<void> _openDownloadUrl(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        Get.snackbar('更新失败', '无法打开下载链接');
      }
    } catch (e) {
      Get.snackbar('更新失败', '打开下载链接时出错: $e');
    }
  }

  /// 判断是否应该检查更新
  bool _shouldCheckForUpdates() {
    final lastCheckTime = _prefs.getInt(_lastCheckTimeKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;
    return now - lastCheckTime > _checkInterval.inMilliseconds;
  }
}
