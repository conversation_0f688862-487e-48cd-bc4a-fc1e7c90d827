from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func

from ..database import get_db
from ..models import User, Novel, Announcement
from ..auth import get_current_admin_user

router = APIRouter(
    prefix="/api/v1/stats",
    tags=["stats"],
    responses={404: {"description": "Not found"}},
)

@router.get("/")
async def get_stats(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """获取系统统计数据（仅管理员）"""
    # 获取用户总数
    user_count = db.query(func.count(User.id)).scalar()
    
    # 获取小说总数
    novel_count = db.query(func.count(Novel.id)).scalar()
    
    # 获取活跃公告数
    active_announcement_count = db.query(func.count(Announcement.id)).filter(Announcement.is_active == True).scalar()
    
    # 获取VIP用户数
    vip_user_count = db.query(func.count(User.id)).filter(User.is_vip == True).scalar()
    
    # 获取管理员用户数
    admin_user_count = db.query(func.count(User.id)).filter(User.is_admin == True).scalar()
    
    return {
        "userCount": user_count,
        "novelCount": novel_count,
        "activeAnnouncementCount": active_announcement_count,
        "vipUserCount": vip_user_count,
        "adminUserCount": admin_user_count
    }
