from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional

from ..database import get_db
from ..models import User, Novel, Chapter
from ..schemas import NovelCreate, NovelResponse, NovelUpdate, ChapterCreate, ChapterResponse
from ..auth import get_current_active_user

router = APIRouter(
    prefix="/api/v1/novels",
    tags=["novels"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=NovelResponse)
async def create_novel(
    novel: NovelCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    db_novel = Novel(
        title=novel.title,
        genre=novel.genre,
        outline=novel.outline,
        content=novel.content,
        style=novel.style,
        session_id=novel.session_id,
        user_id=current_user.id
    )
    db.add(db_novel)
    db.commit()
    db.refresh(db_novel)
    return db_novel

@router.get("/", response_model=List[NovelResponse])
async def read_novels(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    novels = db.query(Novel).filter(Novel.user_id == current_user.id).offset(skip).limit(limit).all()
    return novels

@router.get("/{novel_id}", response_model=NovelResponse)
async def read_novel(
    novel_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    novel = db.query(Novel).filter(Novel.id == novel_id).first()
    if novel is None:
        raise HTTPException(status_code=404, detail="小说未找到")
    if novel.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限访问此小说")
    return novel

@router.put("/{novel_id}", response_model=NovelResponse)
async def update_novel(
    novel_id: str,
    novel_update: NovelUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    db_novel = db.query(Novel).filter(Novel.id == novel_id).first()
    if db_novel is None:
        raise HTTPException(status_code=404, detail="小说未找到")
    if db_novel.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限修改此小说")
    
    # 更新小说信息
    for key, value in novel_update.dict(exclude_unset=True).items():
        setattr(db_novel, key, value)
    
    db.commit()
    db.refresh(db_novel)
    return db_novel

@router.delete("/{novel_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_novel(
    novel_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    db_novel = db.query(Novel).filter(Novel.id == novel_id).first()
    if db_novel is None:
        raise HTTPException(status_code=404, detail="小说未找到")
    if db_novel.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限删除此小说")
    
    db.delete(db_novel)
    db.commit()
    return {"status": "success"}

@router.post("/{novel_id}/chapters", response_model=ChapterResponse)
async def create_chapter(
    novel_id: str,
    chapter: ChapterCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    db_novel = db.query(Novel).filter(Novel.id == novel_id).first()
    if db_novel is None:
        raise HTTPException(status_code=404, detail="小说未找到")
    if db_novel.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限为此小说添加章节")
    
    db_chapter = Chapter(
        number=chapter.number,
        title=chapter.title,
        content=chapter.content,
        novel_id=novel_id
    )
    db.add(db_chapter)
    db.commit()
    db.refresh(db_chapter)
    return db_chapter
