import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:novel_app/models/chat_message.dart';
import 'package:novel_app/models/chat_session.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

/// 对话历史服务，用于管理小说生成过程中的对话历史
class ChatHistoryService extends GetxService {
  static const String _boxName = 'chat_history';
  static const String _sessionsBoxName = 'chat_sessions';
  late Box<Map> _box;
  Box<ChatSession>? _sessionsBox; // 使用可空类型，避免late初始化错误

  // 当前加载的对话历史
  final RxList<ChatMessage> messages = <ChatMessage>[].obs;

  // 当前选中的小说标题
  final RxString currentNovelTitle = ''.obs;

  // 当前选中的会话ID
  final RxString currentSessionId = ''.obs;

  // 所有聊天会话
  final RxList<ChatSession> sessions = <ChatSession>[].obs;

  /// 初始化服务
  Future<ChatHistoryService> init() async {
    try {
      // Web平台不需要初始化Hive路径，已在main.dart中完成
      if (!kIsWeb) {
        // 非Web平台的初始化代码已在main.dart中完成
      }

      // 确保适配器已注册
      print('[ChatHistoryService] 检查适配器注册状态');
      if (!Hive.isAdapterRegistered(6)) {
        print('[ChatHistoryService] 注册 ChatSessionAdapter');
        Hive.registerAdapter(ChatSessionAdapter());
      } else {
        print('[ChatHistoryService] ChatSessionAdapter 已注册');
      }

      if (!Hive.isAdapterRegistered(7)) {
        print('[ChatHistoryService] 注册 ChatSessionTypeAdapter');
        Hive.registerAdapter(ChatSessionTypeAdapter());
      } else {
        print('[ChatHistoryService] ChatSessionTypeAdapter 已注册');
      }

      // 打开消息Box，检查是否已经打开
      try {
        print('[ChatHistoryService] 尝试打开消息盒子: $_boxName');
        if (Hive.isBoxOpen(_boxName)) {
          _box = Hive.box<Map>(_boxName);
          print('[ChatHistoryService] 消息盒子已经打开，直接使用');
        } else {
          _box = await Hive.openBox<Map>(_boxName);
          print('[ChatHistoryService] 已打开消息盒子');
        }
      } catch (e) {
        print('[ChatHistoryService] 打开消息盒子失败: $e');
        // 创建一个内存中的盒子作为备用
        _box = await Hive.openBox<Map>(_boxName, path: '');
      }

      // 打开会话盒子
      try {
        print('[ChatHistoryService] 尝试打开会话盒子: $_sessionsBoxName');

        // 检查会话盒子是否已经打开
        if (Hive.isBoxOpen(_sessionsBoxName)) {
          print('[ChatHistoryService] 会话盒子已经打开，尝试获取');

          // 尝试获取已打开的盒子
          try {
            _sessionsBox = Hive.box<ChatSession>(_sessionsBoxName);
            print('[ChatHistoryService] 成功获取已打开的会话盒子');
          } catch (boxError) {
            print('[ChatHistoryService] 获取已打开的会话盒子失败: $boxError');

            // 尝试关闭并重新打开盒子
            try {
              print('[ChatHistoryService] 尝试关闭并重新打开会话盒子');
              await Hive.box(_sessionsBoxName).close();
              _sessionsBox = await Hive.openBox<ChatSession>(_sessionsBoxName);
              print('[ChatHistoryService] 成功重新打开会话盒子');
            } catch (reopenError) {
              print('[ChatHistoryService] 重新打开会话盒子失败: $reopenError');
              throw reopenError;
            }
          }
        } else {
          print('[ChatHistoryService] 会话盒子未打开，尝试打开');
          _sessionsBox = await Hive.openBox<ChatSession>(_sessionsBoxName);
          print('[ChatHistoryService] 已打开会话盒子');
        }

        // 检查会话盒子是否成功初始化
        if (_sessionsBox == null) {
          print('[ChatHistoryService] 会话盒子初始化后仍为null');
          throw Exception('会话盒子初始化失败');
        }

        // 加载所有会话
        _loadAllSessions();

        print(
            '[ChatHistoryService] 初始化成功，已加载 ${_box.length} 条对话记录，${_sessionsBox?.length ?? 0} 个会话');
      } catch (e) {
        print('[ChatHistoryService] 初始化会话盒子失败: $e');
        // 尝试创建一个内存中的会话盒子作为备用
        try {
          print('[ChatHistoryService] 尝试创建内存会话盒子');
          if (!Hive.isBoxOpen(_sessionsBoxName)) {
            _sessionsBox =
                await Hive.openBox<ChatSession>(_sessionsBoxName, path: '');
            print('[ChatHistoryService] 成功创建内存会话盒子');
          } else {
            print('[ChatHistoryService] 尝试获取已打开的内存会话盒子');
            _sessionsBox = Hive.box<ChatSession>(_sessionsBoxName);
            print('[ChatHistoryService] 成功获取已打开的内存会话盒子');
          }
        } catch (innerE) {
          print('[ChatHistoryService] 创建内存会话盒子也失败: $innerE');
          _sessionsBox = null;
        }
        sessions.clear();
      }

      return this;
    } catch (e) {
      print('[ChatHistoryService] 初始化失败: $e');
      // 创建一个内存中的Box作为备用
      _box = await Hive.openBox<Map>(_boxName, path: '');
      _sessionsBox =
          await Hive.openBox<ChatSession>(_sessionsBoxName, path: '');
      return this;
    }
  }

  /// 加载所有会话
  void _loadAllSessions() {
    try {
      if (_sessionsBox == null) {
        print('[ChatHistoryService] 会话盒子未初始化，无法加载会话');
        sessions.clear();
        return;
      }

      final allSessions = _sessionsBox!.values.toList();

      // 按最后更新时间排序，最新的在前面
      allSessions.sort((a, b) => b.lastUpdatedAt.compareTo(a.lastUpdatedAt));

      sessions.assignAll(allSessions);
      print('[ChatHistoryService] 已加载 ${sessions.length} 个会话');
    } catch (e) {
      print('[ChatHistoryService] 加载会话失败: $e');
      sessions.clear();
    }
  }

  /// 添加一条消息到历史记录
  Future<void> addMessage(ChatMessage message) async {
    try {
      // 添加到内存中的列表
      messages.add(message);

      // 保存到Hive
      final Map<String, dynamic> messageMap = {
        'id': message.id,
        'content': message.content,
        'timestamp': message.timestamp.millisecondsSinceEpoch,
        'type': message.type.index,
        'novelTitle': message.novelTitle,
        'chapterNumber': message.chapterNumber,
        'sessionId': message.sessionId,
      };

      await _box.put(message.id, messageMap);
      print('[ChatHistoryService] 已添加消息: ${message.id}');

      // 如果有会话ID，更新会话的最后更新时间
      if (_sessionsBox != null &&
          message.sessionId != null &&
          message.sessionId!.isNotEmpty) {
        final session = _sessionsBox!.get(message.sessionId!);
        if (session != null) {
          session.updateLastUpdatedAt();
          await _sessionsBox!.put(message.sessionId!, session);
        }
      }
    } catch (e) {
      print('[ChatHistoryService] 添加消息失败: $e');
    }
  }

  /// 加载指定小说的对话历史
  Future<List<ChatMessage>> loadChatHistory(String novelTitle) async {
    try {
      currentNovelTitle.value = novelTitle;
      messages.clear();

      // 从Hive加载数据
      final allMessages = _box.values.toList();

      // 过滤出指定小说的消息
      final novelMessages =
          allMessages.where((map) => map['novelTitle'] == novelTitle).toList();

      // 按时间戳排序
      novelMessages.sort(
          (a, b) => (a['timestamp'] as int).compareTo(b['timestamp'] as int));

      // 转换为ChatMessage对象
      final result = novelMessages
          .map((map) => ChatMessage(
                id: map['id'] as String,
                content: map['content'] as String,
                timestamp: DateTime.fromMillisecondsSinceEpoch(
                    map['timestamp'] as int),
                type: ChatMessageType.values[map['type'] as int],
                novelTitle: map['novelTitle'] as String,
                chapterNumber: map['chapterNumber'] as int?,
              ))
          .toList();

      messages.assignAll(result);
      print(
          '[ChatHistoryService] 已加载 ${messages.length} 条 "$novelTitle" 的对话记录');
      return messages;
    } catch (e) {
      print('[ChatHistoryService] 加载对话历史失败: $e');
      return [];
    }
  }

  /// 清空指定小说的对话历史
  Future<void> clearChatHistory(String novelTitle) async {
    try {
      // 找出所有属于该小说的消息ID
      final messageIds = _box.values
          .where((map) => map['novelTitle'] == novelTitle)
          .map((map) => map['id'] as String)
          .toList();

      // 从Hive中删除
      for (final id in messageIds) {
        await _box.delete(id);
      }

      // 如果当前加载的是这个小说的历史，则清空内存中的列表
      if (currentNovelTitle.value == novelTitle) {
        messages.clear();
      }

      print('[ChatHistoryService] 已清空 "$novelTitle" 的对话历史');
    } catch (e) {
      print('[ChatHistoryService] 清空对话历史失败: $e');
    }
  }

  /// 获取所有小说标题列表
  List<String> getAllNovelTitles() {
    try {
      final titles = _box.values
          .map((map) => map['novelTitle'] as String)
          .toSet()
          .toList();

      return titles;
    } catch (e) {
      print('[ChatHistoryService] 获取小说标题列表失败: $e');
      return [];
    }
  }

  /// 创建新的普通聊天会话
  Future<ChatSession> createNormalChatSession(String title) async {
    try {
      print('[ChatHistoryService] 开始创建新的普通聊天会话: $title');

      // 检查会话盒子是否已初始化
      if (_sessionsBox == null) {
        print('[ChatHistoryService] 会话盒子为null，尝试重新初始化');

        // 尝试重新打开会话盒子
        try {
          if (Hive.isBoxOpen(_sessionsBoxName)) {
            print('[ChatHistoryService] 会话盒子已经打开，尝试获取');
            _sessionsBox = Hive.box<ChatSession>(_sessionsBoxName);
          } else {
            print('[ChatHistoryService] 会话盒子未打开，尝试打开');
            _sessionsBox = await Hive.openBox<ChatSession>(_sessionsBoxName);
          }

          if (_sessionsBox == null) {
            throw Exception('会话盒子重新初始化失败');
          }

          print('[ChatHistoryService] 会话盒子重新初始化成功');
        } catch (boxError) {
          print('[ChatHistoryService] 会话盒子重新初始化失败: $boxError');
          throw Exception('会话盒子未初始化，无法创建会话: $boxError');
        }
      }

      print('[ChatHistoryService] 创建会话对象');
      final session = ChatSession.createNormal(title: title);

      print('[ChatHistoryService] 保存会话到盒子，ID: ${session.id}');
      await _sessionsBox!.put(session.id, session);

      // 验证会话是否成功保存
      final savedSession = _sessionsBox!.get(session.id);
      if (savedSession == null) {
        throw Exception('会话保存失败，无法从盒子中检索');
      }

      print('[ChatHistoryService] 会话成功保存到盒子');

      // 重新加载所有会话
      _loadAllSessions();

      // 设置为当前会话
      currentSessionId.value = session.id;
      currentNovelTitle.value = '';

      // 清空当前消息列表
      messages.clear();

      print('[ChatHistoryService] 已创建新的普通聊天会话: ${session.title}');
      return session;
    } catch (e) {
      print('[ChatHistoryService] 创建普通聊天会话失败: $e');
      rethrow;
    }
  }

  /// 创建新的小说对话会话
  Future<ChatSession> createNovelChatSession(String novelTitle) async {
    try {
      // 检查会话盒子是否已初始化
      if (_sessionsBox == null) {
        throw Exception('会话盒子未初始化，无法创建会话');
      }

      final session = ChatSession.createNovel(novelTitle: novelTitle);
      await _sessionsBox!.put(session.id, session);

      // 重新加载所有会话
      _loadAllSessions();

      // 设置为当前会话
      currentSessionId.value = session.id;
      currentNovelTitle.value = novelTitle;

      // 清空当前消息列表
      messages.clear();

      print('[ChatHistoryService] 已创建新的小说对话会话: ${session.title}');
      return session;
    } catch (e) {
      print('[ChatHistoryService] 创建小说对话会话失败: $e');
      rethrow;
    }
  }

  /// 加载指定会话的对话历史
  Future<List<ChatMessage>> loadSessionHistory(String sessionId) async {
    try {
      // 检查会话盒子是否已初始化
      if (_sessionsBox == null) {
        throw Exception('会话盒子未初始化，无法加载会话');
      }

      // 查找会话
      final session = _sessionsBox!.get(sessionId);
      if (session == null) {
        throw Exception('会话不存在');
      }

      // 设置当前会话
      currentSessionId.value = sessionId;

      // 如果是小说对话，设置小说标题
      if (session.type == ChatSessionType.novel && session.novelTitle != null) {
        currentNovelTitle.value = session.novelTitle!;
      } else {
        currentNovelTitle.value = '';
      }

      // 清空当前消息列表
      messages.clear();

      // 从Hive加载数据
      final allMessages = _box.values.toList();

      // 过滤出指定会话的消息
      final sessionMessages =
          allMessages.where((map) => map['sessionId'] == sessionId).toList();

      // 按时间戳排序
      sessionMessages.sort(
          (a, b) => (a['timestamp'] as int).compareTo(b['timestamp'] as int));

      // 转换为ChatMessage对象
      final result = sessionMessages
          .map((map) => ChatMessage(
                id: map['id'] as String,
                content: map['content'] as String,
                timestamp: DateTime.fromMillisecondsSinceEpoch(
                    map['timestamp'] as int),
                type: ChatMessageType.values[map['type'] as int],
                novelTitle: map['novelTitle'] as String,
                chapterNumber: map['chapterNumber'] as int?,
              ))
          .toList();

      messages.assignAll(result);

      // 更新会话的最后更新时间
      session.updateLastUpdatedAt();
      await _sessionsBox!.put(sessionId, session);

      print(
          '[ChatHistoryService] 已加载 ${messages.length} 条会话 "$sessionId" 的对话记录');
      return messages;
    } catch (e) {
      print('[ChatHistoryService] 加载会话历史失败: $e');
      return [];
    }
  }

  /// 删除会话
  Future<void> deleteSession(String sessionId) async {
    try {
      // 检查会话盒子是否已初始化
      if (_sessionsBox == null) {
        throw Exception('会话盒子未初始化，无法删除会话');
      }

      // 删除会话
      await _sessionsBox!.delete(sessionId);

      // 删除会话相关的所有消息
      final messageIds = _box.values
          .where((map) => map['sessionId'] == sessionId)
          .map((map) => map['id'] as String)
          .toList();

      for (final id in messageIds) {
        await _box.delete(id);
      }

      // 重新加载所有会话
      _loadAllSessions();

      // 如果当前会话是被删除的会话，清空当前会话
      if (currentSessionId.value == sessionId) {
        currentSessionId.value = '';
        currentNovelTitle.value = '';
        messages.clear();
      }

      print('[ChatHistoryService] 已删除会话: $sessionId');
    } catch (e) {
      print('[ChatHistoryService] 删除会话失败: $e');
      rethrow;
    }
  }

  /// 继续对话，添加用户消息并返回
  Future<ChatMessage> continueChat(String userMessage,
      {int? chapterNumber, String? sessionId}) async {
    // 检查是否有当前会话
    if ((currentSessionId.value.isEmpty && currentNovelTitle.value.isEmpty) &&
        sessionId == null) {
      throw Exception('未选择会话或小说，无法继续对话');
    }

    // 使用传入的sessionId或当前会话ID
    final String actualSessionId = sessionId ?? currentSessionId.value;

    // 如果有会话ID但没有小说标题，使用会话ID作为novelTitle
    final String novelTitle = currentNovelTitle.value.isNotEmpty
        ? currentNovelTitle.value
        : actualSessionId;

    final message = ChatMessage.user(
      content: userMessage,
      novelTitle: novelTitle,
      chapterNumber: chapterNumber,
      sessionId: actualSessionId,
    );

    await addMessage(message);

    // 如果有会话ID，更新会话的最后更新时间和摘要
    if (_sessionsBox != null && actualSessionId.isNotEmpty) {
      final session = _sessionsBox!.get(actualSessionId);
      if (session != null) {
        session.updateLastUpdatedAt();
        // 更新摘要为最新的用户消息
        session.updateSummary(userMessage.length > 50
            ? '${userMessage.substring(0, 47)}...'
            : userMessage);
        await _sessionsBox!.put(actualSessionId, session);
      }
    }

    return message;
  }

  /// 添加AI回复
  Future<ChatMessage> addAIReply(String aiMessage,
      {int? chapterNumber, String? sessionId}) async {
    // 检查是否有当前会话
    if ((currentSessionId.value.isEmpty && currentNovelTitle.value.isEmpty) &&
        sessionId == null) {
      throw Exception('未选择会话或小说，无法添加AI回复');
    }

    // 使用传入的sessionId或当前会话ID
    final String actualSessionId = sessionId ?? currentSessionId.value;

    // 如果有会话ID但没有小说标题，使用会话ID作为novelTitle
    final String novelTitle = currentNovelTitle.value.isNotEmpty
        ? currentNovelTitle.value
        : actualSessionId;

    final message = ChatMessage.ai(
      content: aiMessage,
      novelTitle: novelTitle,
      chapterNumber: chapterNumber,
      sessionId: actualSessionId,
    );

    await addMessage(message);
    return message;
  }
}
