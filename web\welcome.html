<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>岱宗文脉 - AI驱动的小说创作平台</title>
    <link rel="stylesheet" href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap">
    <style>
        :root {
            --primary-color: #9ACD32; /* 黄绿色 */
            --primary-dark: #7BA428;
            --primary-light: #C1E467;
            --text-color: #333333;
            --bg-color: #FFFFFF;
            --secondary-bg: #F8F9FA;
            --accent-color: #FF8C00; /* 深橙色作为点缀 */
            --gradient-start: #9ACD32;
            --gradient-end: #8BC34A;
        }

        .dark {
            --primary-color: #AADD44;
            --primary-dark: #8BC34A;
            --primary-light: #CDDC39;
            --text-color: #E1E1E1;
            --bg-color: #121212;
            --secondary-bg: #1E1E1E;
            --accent-color: #FFA726;
            --gradient-start: #8BC34A;
            --gradient-end: #689F38;
        }

        body {
            font-family: 'Noto Sans SC', Tahoma, Arial, Roboto, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", Simsun, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-color);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Noto Serif SC', serif;
        }

        .primary-bg {
            background-color: var(--primary-color);
        }

        .primary-text {
            color: var(--primary-color);
        }

        .accent-text {
            color: var(--accent-color);
        }

        .secondary-bg {
            background-color: var(--secondary-bg);
        }

        .hero-gradient {
            background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .logo-text {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--primary-dark), var(--accent-color));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .theme-toggle {
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .theme-toggle:hover {
            transform: rotate(30deg);
        }

        /* 首字下沉效果 */
        .first-letter::first-letter {
            font-size: 3.5em;
            float: left;
            line-height: 0.8;
            margin-right: 0.1em;
            color: var(--primary-color);
            font-weight: bold;
        }

        /* 微交互动画 */
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        .float-animation {
            animation: float 3s ease-in-out infinite;
        }

        .btn-start {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            transition: all 0.3s ease;
            transform-origin: center;
        }

        .btn-start:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }

        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="sticky top-0 z-50 bg-opacity-90 backdrop-filter backdrop-blur-sm" style="background-color: var(--bg-color);">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="w-10 h-10 rounded-full hero-gradient flex items-center justify-center">
                    <i class="fas fa-feather-alt text-white text-xl"></i>
                </div>
                <span class="text-xl font-bold primary-text">岱宗文脉</span>
            </div>
            <div class="flex items-center space-x-4">
                <button id="themeToggle" class="theme-toggle p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                    <i class="fas fa-moon text-xl"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero 区域 -->
    <section class="py-16 md:py-24 hero-gradient text-white">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <h1 class="text-4xl md:text-6xl font-bold mb-6">岱宗文脉</h1>
                    <p class="text-xl md:text-2xl mb-8">汲取泰山灵气，承载文脉传承</p>
                    <p class="text-lg mb-10 max-w-lg">借助AI的力量，释放您的创作潜能，轻松创作引人入胜的小说作品</p>
                    <a href="index.html?direct=true" class="btn-start text-xl font-bold py-4 px-10 rounded-full inline-block shadow-lg">
                        快速开始
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
                <div class="md:w-1/2 flex justify-center">
                    <div class="relative float-animation">
                        <svg width="300" height="300" viewBox="0 0 300 300" class="drop-shadow-2xl">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stop-color="#9ACD32" />
                                    <stop offset="100%" stop-color="#FF8C00" />
                                </linearGradient>
                            </defs>
                            <circle cx="150" cy="150" r="130" fill="white" />
                            <circle cx="150" cy="150" r="110" fill="url(#logoGradient)" />
                            <!-- 书本形状 -->
                            <path d="M120,100 L120,200 L180,200 C190,200 200,190 200,180 L200,120 C200,110 190,100 180,100 L120,100 Z" fill="white" />
                            <path d="M115,95 L115,205 L180,205 C195,205 205,195 205,180 L205,120 C205,105 195,95 180,95 L115,95 Z M110,90 L180,90 C200,90 210,100 210,120 L210,180 C210,200 200,210 180,210 L110,210 L110,90 Z" fill="#9ACD32" />
                            <!-- 羽毛笔 -->
                            <path d="M160,130 C160,130 180,110 200,130 C220,150 180,190 160,170 L160,130 Z" fill="#FF8C00" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 特色功能区 -->
    <section class="py-16 secondary-bg">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">强大功能，释放创作力</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
                    <div class="text-4xl primary-text mb-4">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">AI辅助创作</h3>
                    <p>利用先进的AI技术，根据您的创意自动生成精彩的小说内容，让创作过程更加流畅</p>
                </div>
                <div class="card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
                    <div class="text-4xl primary-text mb-4">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">智能大纲管理</h3>
                    <p>轻松创建和管理小说大纲，AI智能分析情节发展，提供合理的创作建议</p>
                </div>
                <div class="card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
                    <div class="text-4xl primary-text mb-4">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">角色塑造系统</h3>
                    <p>打造丰满立体的小说角色，AI助您设计角色性格、背景故事和发展轨迹</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 快速开始区域 -->
    <section class="py-16">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-6">开始您的创作之旅</h2>
            <p class="text-lg mb-10 max-w-2xl mx-auto">无论您是经验丰富的作家还是初次尝试创作的新手，岱宗文脉都能为您提供所需的工具和灵感</p>
            <a href="index.html?direct=true" class="btn-start text-xl font-bold py-4 px-10 rounded-full inline-block shadow-lg">
                立即体验
                <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="py-8 hero-gradient text-white">
        <div class="container mx-auto px-4 text-center">
            <p>© 2023-2024 岱宗文脉 - AI驱动的小说创作平台</p>
            <p class="mt-2">汲取泰山灵气，承载文脉传承</p>
        </div>
    </footer>

    <script>
        // 主题切换功能
        const themeToggle = document.getElementById('themeToggle');
        const htmlElement = document.documentElement;
        const themeIcon = themeToggle.querySelector('i');

        // 检查系统偏好
        const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;

        // 检查本地存储
        const savedTheme = localStorage.getItem('theme');

        // 设置初始主题
        if (savedTheme) {
            htmlElement.className = savedTheme;
            updateThemeIcon(savedTheme === 'dark');
        } else if (prefersDarkMode) {
            htmlElement.className = 'dark';
            updateThemeIcon(true);
        }

        // 切换主题
        themeToggle.addEventListener('click', () => {
            const isDark = htmlElement.classList.contains('dark');
            htmlElement.className = isDark ? 'light' : 'dark';
            localStorage.setItem('theme', isDark ? 'light' : 'dark');
            updateThemeIcon(!isDark);
        });

        function updateThemeIcon(isDark) {
            themeIcon.className = isDark ? 'fas fa-sun text-xl' : 'fas fa-moon text-xl';
        }
    </script>
</body>
</html>
