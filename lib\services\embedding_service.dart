import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/models/embedding_model_config.dart';

/// 嵌入服务，用于处理文本嵌入和相似度检索
class EmbeddingService extends GetxService {
  final ApiConfigController _apiConfigController;

  EmbeddingService({
    required ApiConfigController apiConfigController,
  }) : _apiConfigController = apiConfigController;

  /// 获取文本的嵌入向量
  Future<List<double>> getEmbedding(String text) async {
    final config = _apiConfigController.embeddingModel.value;

    if (!config.enabled) {
      throw Exception('嵌入模型未启用');
    }

    try {
      final response = await _callEmbeddingApi(text, config);
      return _parseEmbeddingResponse(response, config);
    } catch (e) {
      print('[EmbeddingService] 获取嵌入向量失败: $e');
      rethrow;
    }
  }

  /// 验证嵌入模型连接
  /// 返回一个包含成功/失败状态和消息的Map
  Future<Map<String, dynamic>> validateConnection(
      EmbeddingModelConfig config) async {
    try {
      // 检查是否使用岱宗官方嵌入模型
      bool isUsingDaizongOfficial =
          config.apiKey == 'daizong_official_embedding_key';

      // 如果是岱宗官方嵌入模型，直接返回成功
      if (isUsingDaizongOfficial) {
        return {
          'success': true,
          'message': '岱宗官方嵌入模型验证成功（限时免费）',
          'official': true,
        };
      }

      // 首先检查API密钥
      if (config.apiKey.isEmpty) {
        return {
          'success': false,
          'message': '验证失败: 缺少API密钥\n\n请在设置中填写有效的API密钥',
        };
      }

      // 检查API路径是否包含模型名称
      if (config.apiFormat == 'Google API' &&
          !config.apiPath.contains(config.modelName)) {
        return {
          'success': false,
          'message':
              '验证失败: API路径不正确\n\n正确的API路径应为: /v1beta/models/${config.modelName}:embedContent',
        };
      }

      // 检查阿里百炼的API路径
      if (config.apiFormat == '阿里百炼' &&
          !config.apiPath.contains('embeddings')) {
        return {
          'success': false,
          'message':
              '验证失败: API路径不正确\n\n阿里百炼的正确 API路径应为: /api/v1/services/embeddings/text-embedding/text-embedding',
        };
      }

      // 使用一个简单的测试文本
      const testText = '这是一个测试文本，用于验证嵌入模型连接。';

      // 调用API
      final response = await _callEmbeddingApi(testText, config);

      // 解析响应
      final embeddings = _parseEmbeddingResponse(response, config);

      // 检查嵌入向量是否有效
      if (embeddings.isEmpty) {
        return {
          'success': false,
          'message': '验证失败: 返回的嵌入向量为空',
        };
      }

      // 返回成功信息和向量维度
      return {
        'success': true,
        'message': '验证成功! 嵌入向量维度: ${embeddings.length}',
        'dimensions': embeddings.length,
      };
    } catch (e) {
      print('[EmbeddingService] 验证连接失败: $e');

      String errorMessage = e.toString();
      String helpMessage = '';

      // 根据错误类型提供更有用的提示
      if (errorMessage.contains('401') ||
          errorMessage.contains('UNAUTHENTICATED')) {
        helpMessage =
            '\n\n请确保您的API密钥有效且正确输入。\n您可以在Google AI Studio获取新的API密钥: https://makersuite.google.com/app/apikey';
      } else if (errorMessage.contains('timeout') ||
          errorMessage.contains('超时')) {
        helpMessage =
            '\n\n连接超时，请尝试：\n1. 增加超时时间\n2. 启用代理并设置正确的代理服务器\n3. 检查您的网络连接';
      }

      return {
        'success': false,
        'message': '验证失败: $errorMessage$helpMessage',
      };
    }
  }

  /// 计算两个文本的相似度
  Future<double> calculateSimilarity(String text1, String text2) async {
    try {
      final embedding1 = await getEmbedding(text1);
      final embedding2 = await getEmbedding(text2);

      return _cosineSimilarity(embedding1, embedding2);
    } catch (e) {
      print('[EmbeddingService] 计算相似度失败: $e');
      return 0.0;
    }
  }

  /// 从多个文本中找出与查询文本最相似的topK个文本
  Future<List<Map<String, dynamic>>> findSimilarTexts(
      String queryText, List<Map<String, dynamic>> documents,
      {String textField = 'text'}) async {
    if (documents.isEmpty) {
      return [];
    }

    try {
      final config = _apiConfigController.embeddingModel.value;
      final queryEmbedding = await getEmbedding(queryText);

      // 计算每个文档与查询文本的相似度
      final List<Map<String, dynamic>> results = [];

      for (final doc in documents) {
        final text = doc[textField] as String? ?? '';
        if (text.isEmpty) continue;

        final embedding = await getEmbedding(text);
        final similarity = _cosineSimilarity(queryEmbedding, embedding);

        results.add({
          ...doc,
          'similarity': similarity,
        });
      }

      // 按相似度排序
      results.sort((a, b) =>
          (b['similarity'] as double).compareTo(a['similarity'] as double));

      // 返回topK个结果
      return results.take(config.topK).toList();
    } catch (e) {
      print('[EmbeddingService] 查找相似文本失败: $e');
      return [];
    }
  }

  /// 调用嵌入API
  Future<Map<String, dynamic>> _callEmbeddingApi(
      String text, EmbeddingModelConfig config) async {
    // 检查是否使用岱宗官方嵌入模型
    bool isUsingDaizongOfficial =
        config.apiKey == 'daizong_official_embedding_key';

    // 构建 URL，对于 Google API，需要将 API 密钥作为 URL 参数
    String url;
    if (config.apiFormat == 'Google API') {
      // Google API 使用 URL 参数传递 API 密钥
      url = '${config.baseUrl}${config.apiPath}?key=${config.apiKey}';
    } else {
      url = '${config.baseUrl}${config.apiPath}';
    }

    Map<String, String> headers = {
      'Content-Type': 'application/json',
    };

    // 对于非 Google API，使用 Authorization 头
    if (config.apiKey.isNotEmpty) {
      if (isUsingDaizongOfficial) {
        // 使用岱宗官方API密钥
        headers['Authorization'] = 'Bearer sk-f9f2cd5cc4d24715b9c3142ad0ef0f9c';
        headers['X-DashScope-SSE'] = 'disable'; // 禁用SSE流式响应
        headers['X-DashScope-Async'] = 'disable'; // 禁用异步调用
      } else if (config.apiFormat == 'Google API') {
        // Google API 使用 URL 参数传递 API 密钥，不需要设置头部
      } else if (config.apiFormat == '阿里百炼') {
        // 阿里百炼使用特定的认证头
        headers['Authorization'] = 'Bearer ${config.apiKey}';
        headers['X-DashScope-SSE'] = 'disable'; // 禁用SSE流式响应
        headers['X-DashScope-Async'] = 'disable'; // 禁用异步调用
      } else {
        // 默认使用Bearer认证
        headers['Authorization'] = config.apiKey.startsWith('Bearer ')
            ? config.apiKey
            : 'Bearer ${config.apiKey}';
      }
    }

    Map<String, dynamic> body;

    switch (config.apiFormatEnum) {
      case EmbeddingApiFormat.openAiCompatible:
        body = {
          'model': config.modelName,
          'input': text,
        };
        break;
      case EmbeddingApiFormat.googleApi:
        // Google Gemini API格式，模型名称已在URL中指定
        body = {
          'content': {
            'parts': [
              {'text': text}
            ]
          },
        };
        break;
      case EmbeddingApiFormat.aliBailian:
        // 阿里百炼特定格式
        body = {
          'model': config.modelName,
          'input': {
            'texts': [text]
          },
          'parameters': {'text_type': 'query'}
        };
        break;
      case EmbeddingApiFormat.azureOpenAi:
        // Azure OpenAI 格式
        body = {
          'input': text,
        };
        break;
      case EmbeddingApiFormat.ollama:
        // Ollama 格式
        body = {
          'model': config.modelName,
          'prompt': text,
        };
        break;
      case EmbeddingApiFormat.mlStudio:
        // ML Studio 格式 (类似OpenAI)
        body = {
          'model': config.modelName,
          'input': text,
        };
        break;
      case EmbeddingApiFormat.gemini:
        // Gemini 格式
        body = {
          'model': config.modelName,
          'content': {
            'parts': [
              {'text': text}
            ]
          },
        };
        break;
      case EmbeddingApiFormat.siliconFlow:
        // SiliconFlow 格式
        body = {
          'model': config.modelName,
          'input': text,
          'encoding_format': 'float',
        };
        break;
      default:
        throw Exception('不支持的API格式: ${config.apiFormat}');
    }

    http.Client client;

    // 如果用户明确启用了代理，创建代理客户端，否则使用系统默认网络
    if (config.useProxy && config.proxyUrl.isNotEmpty) {
      try {
        final httpClient = HttpClient();
        httpClient.findProxy = (_) {
          return "PROXY ${config.proxyUrl}";
        };
        client = IOClient(httpClient);
      } catch (e) {
        print('[EmbeddingService] 创建代理客户端失败: $e');
        client = http.Client();
      }
    } else {
      // 使用系统默认网络连接（包括VPN）
      client = http.Client();
    }

    try {
      // 设置超时时间
      final response = await client
          .post(
            Uri.parse(url),
            headers: headers,
            body: jsonEncode(body),
          )
          .timeout(Duration(seconds: config.timeout));

      if (response.statusCode != 200) {
        // 尝试解析错误响应
        try {
          final errorJson = jsonDecode(response.body) as Map<String, dynamic>;
          if (errorJson.containsKey('error')) {
            final error = errorJson['error'] as Map<String, dynamic>;
            final code = error['code'];
            final message = error['message'];
            final status = error['status'];
            throw Exception('调用API失败 ($code $status): $message');
          }
        } catch (e) {
          // 如果无法解析错误响应，则返回原始错误
        }

        throw Exception('API调用失败: ${response.statusCode} ${response.body}');
      }

      return jsonDecode(response.body) as Map<String, dynamic>;
    } finally {
      client.close();
    }
  }

  /// 解析嵌入API的响应
  List<double> _parseEmbeddingResponse(
      Map<String, dynamic> response, EmbeddingModelConfig config) {
    try {
      switch (config.apiFormatEnum) {
        case EmbeddingApiFormat.openAiCompatible:
          return _parseOpenAIResponse(response);

        case EmbeddingApiFormat.googleApi:
          return _parseGoogleApiResponse(response);

        case EmbeddingApiFormat.aliBailian:
          return _parseAliBailianResponse(response);

        case EmbeddingApiFormat.azureOpenAi:
          return _parseAzureOpenAIResponse(response);

        case EmbeddingApiFormat.ollama:
          return _parseOllamaResponse(response);

        case EmbeddingApiFormat.mlStudio:
          return _parseMLStudioResponse(response);

        case EmbeddingApiFormat.gemini:
          return _parseGeminiResponse(response);

        case EmbeddingApiFormat.siliconFlow:
          return _parseSiliconFlowResponse(response);
      }
    } catch (e) {
      print('[EmbeddingService] 解析嵌入响应失败: $e');
      print('[EmbeddingService] 响应内容: $response');
      rethrow;
    }
  }

  /// 解析OpenAI兼容API的响应
  List<double> _parseOpenAIResponse(Map<String, dynamic> response) {
    final data = response['data'] as List<dynamic>;
    if (data.isEmpty) {
      throw Exception('API返回的嵌入数据为空');
    }

    final embedding = data[0]['embedding'] as List<dynamic>;
    return embedding.map((e) => (e as num).toDouble()).toList();
  }

  /// 解析Google API的响应
  List<double> _parseGoogleApiResponse(Map<String, dynamic> response) {
    try {
      // 输出详细的响应信息便于调试
      print('[EmbeddingService] Google API 响应结构: ${response.keys.join(', ')}');

      if (response.containsKey('embedding')) {
        // 旧格式
        final embedding = response['embedding']['values'] as List<dynamic>;
        return embedding.map((e) => (e as num).toDouble()).toList();
      } else if (response.containsKey('embeddings')) {
        // text-embedding-004 格式
        final embeddings = response['embeddings'] as List<dynamic>;
        if (embeddings.isEmpty) {
          throw Exception('API返回的嵌入数据为空');
        }
        final values = embeddings[0]['values'] as List<dynamic>;
        return values.map((e) => (e as num).toDouble()).toList();
      } else if (response.containsKey('values')) {
        // 可能的直接返回格式
        final values = response['values'] as List<dynamic>;
        return values.map((e) => (e as num).toDouble()).toList();
      } else {
        // 尝试递归查找嵌入向量
        for (var key in response.keys) {
          if (response[key] is Map<String, dynamic>) {
            try {
              return _parseGoogleApiResponse(
                  response[key] as Map<String, dynamic>);
            } catch (e) {
              // 忽略递归解析错误，继续尝试其他字段
            }
          }
        }

        throw Exception('无法解析Google API响应: ${response.keys.join(', ')}');
      }
    } catch (e) {
      print('[EmbeddingService] 解析Google API响应失败: $e');
      print('[EmbeddingService] 原始响应: $response');
      rethrow;
    }
  }

  /// 解析阿里百炼API的响应
  List<double> _parseAliBailianResponse(Map<String, dynamic> response) {
    try {
      // 输出详细的响应信息便于调试
      print('[EmbeddingService] 阿里百炼响应结构: ${response.keys.join(', ')}');

      if (response.containsKey('output')) {
        final output = response['output'] as Map<String, dynamic>;
        if (output.containsKey('embeddings')) {
          final embeddings = output['embeddings'] as List<dynamic>;
          if (embeddings.isEmpty) {
            throw Exception('API返回的嵌入数据为空');
          }

          // 阿里百炼的嵌入向量格式可能是数组或对象
          final firstEmbedding = embeddings[0];
          if (firstEmbedding is List<dynamic>) {
            // 直接是向量数组
            return firstEmbedding.map((e) => (e as num).toDouble()).toList();
          } else if (firstEmbedding is Map<String, dynamic>) {
            // 是对象，需要提取embedding字段
            if (firstEmbedding.containsKey('embedding')) {
              final embedding = firstEmbedding['embedding'] as List<dynamic>;
              return embedding.map((e) => (e as num).toDouble()).toList();
            } else if (firstEmbedding.containsKey('vector')) {
              final vector = firstEmbedding['vector'] as List<dynamic>;
              return vector.map((e) => (e as num).toDouble()).toList();
            }
          }
        } else if (output.containsKey('vectors')) {
          // 另一种可能的格式
          final vectors = output['vectors'] as List<dynamic>;
          if (vectors.isEmpty) {
            throw Exception('API返回的嵌入数据为空');
          }
          return vectors[0].map((e) => (e as num).toDouble()).toList();
        }
      } else if (response.containsKey('data')) {
        // 可能使用了类似OpenAI的格式
        return _parseOpenAIResponse({'data': response['data']});
      }

      // 如果上面的方法都失败，尝试递归查找
      for (var key in response.keys) {
        if (response[key] is Map<String, dynamic>) {
          try {
            return _parseAliBailianResponse(
                response[key] as Map<String, dynamic>);
          } catch (e) {
            // 忽略递归解析错误，继续尝试其他字段
          }
        }
      }

      throw Exception('无法解析阿里百炼响应: ${response.keys.join(', ')}');
    } catch (e) {
      print('[EmbeddingService] 解析阿里百炼响应失败: $e');
      print('[EmbeddingService] 原始响应: $response');
      rethrow;
    }
  }

  /// 解析Azure OpenAI API的响应
  List<double> _parseAzureOpenAIResponse(Map<String, dynamic> response) {
    try {
      if (response.containsKey('data')) {
        return _parseOpenAIResponse(response);
      } else if (response.containsKey('embeddings')) {
        final embeddings = response['embeddings'] as List<dynamic>;
        if (embeddings.isEmpty) {
          throw Exception('API返回的嵌入数据为空');
        }
        return embeddings[0].map((e) => (e as num).toDouble()).toList();
      }

      throw Exception('无法解析Azure OpenAI响应: ${response.keys.join(', ')}');
    } catch (e) {
      print('[EmbeddingService] 解析Azure OpenAI响应失败: $e');
      print('[EmbeddingService] 原始响应: $response');
      rethrow;
    }
  }

  /// 解析Ollama API的响应
  List<double> _parseOllamaResponse(Map<String, dynamic> response) {
    try {
      if (response.containsKey('embedding')) {
        final embedding = response['embedding'] as List<dynamic>;
        return embedding.map((e) => (e as num).toDouble()).toList();
      }

      throw Exception('无法解析Ollama响应: ${response.keys.join(', ')}');
    } catch (e) {
      print('[EmbeddingService] 解析Ollama响应失败: $e');
      print('[EmbeddingService] 原始响应: $response');
      rethrow;
    }
  }

  /// 解析ML Studio API的响应
  List<double> _parseMLStudioResponse(Map<String, dynamic> response) {
    try {
      // ML Studio通常使用OpenAI兼容格式
      return _parseOpenAIResponse(response);
    } catch (e) {
      print('[EmbeddingService] 解析ML Studio响应失败: $e');
      print('[EmbeddingService] 原始响应: $response');
      rethrow;
    }
  }

  /// 解析Gemini API的响应
  List<double> _parseGeminiResponse(Map<String, dynamic> response) {
    try {
      if (response.containsKey('embedding')) {
        final embedding = response['embedding'];
        if (embedding is Map<String, dynamic> &&
            embedding.containsKey('values')) {
          final values = embedding['values'] as List<dynamic>;
          return values.map((e) => (e as num).toDouble()).toList();
        }
      }

      // 尝试使用Google API解析方法
      return _parseGoogleApiResponse(response);
    } catch (e) {
      print('[EmbeddingService] 解析Gemini响应失败: $e');
      print('[EmbeddingService] 原始响应: $response');
      rethrow;
    }
  }

  /// 解析SiliconFlow API的响应
  List<double> _parseSiliconFlowResponse(Map<String, dynamic> response) {
    try {
      if (response.containsKey('data')) {
        // 类似OpenAI格式
        return _parseOpenAIResponse(response);
      } else if (response.containsKey('embedding')) {
        final embedding = response['embedding'] as List<dynamic>;
        return embedding.map((e) => (e as num).toDouble()).toList();
      }

      throw Exception('无法解析SiliconFlow响应: ${response.keys.join(', ')}');
    } catch (e) {
      print('[EmbeddingService] 解析SiliconFlow响应失败: $e');
      print('[EmbeddingService] 原始响应: $response');
      rethrow;
    }
  }

  /// 计算余弦相似度
  double _cosineSimilarity(List<double> vec1, List<double> vec2) {
    if (vec1.length != vec2.length) {
      throw Exception('向量长度不一致: ${vec1.length} vs ${vec2.length}');
    }

    double dotProduct = 0.0;
    double norm1 = 0.0;
    double norm2 = 0.0;

    for (int i = 0; i < vec1.length; i++) {
      dotProduct += vec1[i] * vec2[i];
      norm1 += vec1[i] * vec1[i];
      norm2 += vec2[i] * vec2[i];
    }

    // 避免除以零
    if (norm1 == 0 || norm2 == 0) {
      return 0.0;
    }

    return dotProduct / (math.sqrt(norm1) * math.sqrt(norm2));
  }
}
