package com.daizhong.novelapp;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

/**
 * 岱宗文脉启动动画Activity
 * 用于显示应用启动动画并处理初始化过程
 */
public class SplashActivity extends AppCompatActivity {

    private ImageView logoImageView;
    private TextView titleTextView;
    private TextView sloganTextView;
    private ProgressBar progressBar;
    private TextView statusTextView;

    private final Handler handler = new Handler(Looper.getMainLooper());
    private final String[] initSteps = {
            "初始化数据存储...",
            "加载用户配置...",
            "初始化AI服务...",
            "加载模型配置...",
            "初始化嵌入模型...",
            "加载知识库...",
            "初始化生成服务...",
            "准备完毕..."
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        // 初始化视图
        logoImageView = findViewById(R.id.logoImageView);
        titleTextView = findViewById(R.id.titleTextView);
        sloganTextView = findViewById(R.id.sloganTextView);
        progressBar = findViewById(R.id.progressBar);
        statusTextView = findViewById(R.id.statusTextView);

        // 开始动画
        startAnimations();
    }

    /**
     * 开始启动动画序列
     */
    private void startAnimations() {
        // 设置初始状态
        logoImageView.setAlpha(0f);
        logoImageView.setScaleX(0.5f);
        logoImageView.setScaleY(0.5f);
        titleTextView.setAlpha(0f);
        sloganTextView.setAlpha(0f);
        progressBar.setAlpha(0f);
        statusTextView.setAlpha(0f);

        // Logo动画
        ObjectAnimator logoFadeIn = ObjectAnimator.ofFloat(logoImageView, View.ALPHA, 0f, 1f);
        logoFadeIn.setDuration(800);

        ObjectAnimator logoScaleX = ObjectAnimator.ofFloat(logoImageView, View.SCALE_X, 0.5f, 1f);
        logoScaleX.setDuration(1000);
        logoScaleX.setInterpolator(new AccelerateDecelerateInterpolator());

        ObjectAnimator logoScaleY = ObjectAnimator.ofFloat(logoImageView, View.SCALE_Y, 0.5f, 1f);
        logoScaleY.setDuration(1000);
        logoScaleY.setInterpolator(new AccelerateDecelerateInterpolator());

        // 标题动画
        ObjectAnimator titleFadeIn = ObjectAnimator.ofFloat(titleTextView, View.ALPHA, 0f, 1f);
        titleFadeIn.setDuration(800);
        titleFadeIn.setStartDelay(300);

        // 标语动画
        ObjectAnimator sloganFadeIn = ObjectAnimator.ofFloat(sloganTextView, View.ALPHA, 0f, 1f);
        sloganFadeIn.setDuration(800);
        sloganFadeIn.setStartDelay(500);

        // 进度条动画
        ObjectAnimator progressFadeIn = ObjectAnimator.ofFloat(progressBar, View.ALPHA, 0f, 1f);
        progressFadeIn.setDuration(500);
        progressFadeIn.setStartDelay(800);

        // 状态文本动画
        ObjectAnimator statusFadeIn = ObjectAnimator.ofFloat(statusTextView, View.ALPHA, 0f, 1f);
        statusFadeIn.setDuration(500);
        statusFadeIn.setStartDelay(800);

        // 启动所有动画
        logoFadeIn.start();
        logoScaleX.start();
        logoScaleY.start();
        titleFadeIn.start();
        sloganFadeIn.start();
        progressFadeIn.start();
        statusFadeIn.start();

        // 动画完成后开始模拟初始化过程
        handler.postDelayed(this::simulateInitialization, 1200);
    }

    /**
     * 模拟初始化过程
     */
    private void simulateInitialization() {
        final int totalSteps = initSteps.length;
        final int stepDuration = 400; // 每个步骤的持续时间(毫秒)

        for (int i = 0; i < totalSteps; i++) {
            final int step = i;
            handler.postDelayed(() -> {
                // 更新状态文本
                statusTextView.setText(initSteps[step]);
                
                // 更新进度条
                int progress = (int) (((step + 1) / (float) totalSteps) * 100);
                ObjectAnimator progressAnimator = ObjectAnimator.ofInt(
                        progressBar, "progress", progressBar.getProgress(), progress);
                progressAnimator.setDuration(300);
                progressAnimator.start();

                // 最后一步完成后启动主Activity
                if (step == totalSteps - 1) {
                    handler.postDelayed(() -> {
                        startMainActivity();
                    }, 500);
                }
            }, i * stepDuration);
        }
    }

    /**
     * 启动主Activity
     */
    private void startMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        
        // 添加淡出过渡动画
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
        
        // 结束启动Activity
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 移除所有回调，防止内存泄漏
        handler.removeCallbacksAndMessages(null);
    }
}
