# 岱宗文脉 Web版部署指南

本文档提供了将岱宗文脉Web版部署到服务器的详细说明。

## 构建Web版本

在部署之前，您需要构建Flutter Web版本：

```bash
# 使用HTML渲染器构建（更兼容但性能较低）
flutter build web --release --web-renderer html

# 或使用CanvasKit渲染器构建（性能更好但文件更大）
flutter build web --release --web-renderer canvaskit
```

构建完成后，所有文件将位于`build/web`目录中。

## 下载CanvasKit到本地

为了避免依赖Google的CDN，您应该下载CanvasKit文件到本地：

```bash
# 安装requests库（如果尚未安装）
pip install requests

# 运行下载脚本
python web/download_canvaskit.py
```

这将下载CanvasKit文件到`web/canvaskit/`目录。确保将这些文件部署到您的服务器上。

## 部署到Web服务器

### 必要文件

确保以下文件都已正确上传到服务器：

- `index.html`
- `flutter.js`
- `flutter_backup.js`
- `flutter_canvaskit_config.js`（新增）
- `main.dart.js`
- `manifest.json`
- `favicon.png`
- `assets/`目录（包含所有资源文件）
- `icons/`目录（包含所有图标）
- `canvaskit/`目录（包含CanvasKit文件）（新增）
- `.htaccess`（Apache服务器）或`web.config`（IIS服务器）

### Apache服务器配置

如果您使用Apache服务器，请确保：

1. 已上传`.htaccess`文件
2. 服务器已启用`mod_rewrite`模块
3. 在虚拟主机配置中允许覆盖：

```apache
<Directory /path/to/web/root>
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
</Directory>
```

### Nginx服务器配置

如果您使用Nginx服务器，请参考`nginx.conf.example`文件，并将其内容添加到您的服务器配置中。

### IIS服务器配置

如果您使用IIS服务器，请确保：

1. 已上传`web.config`文件
2. 已安装URL Rewrite模块
3. 已配置正确的MIME类型

## 常见问题排查

### 1. flutter.js无法加载

如果`flutter.js`无法加载，应用会自动尝试加载`flutter_backup.js`。如果仍然失败，请检查：

- 文件是否已上传到正确位置
- 服务器是否配置了正确的MIME类型
- 是否有防火墙或安全设置阻止JavaScript文件

### 2. 页面加载后显示空白

这可能是由于路由问题导致的。确保服务器配置了正确的URL重写规则，将所有请求重定向到`index.html`。

### 3. API请求失败

如果API请求失败，请检查：

- Content-Security-Policy是否配置正确
- 是否存在CORS问题
- API端点是否可访问

### 4. 资源文件（图片、字体等）无法加载

确保：

- 所有资源文件都已上传
- 服务器配置了正确的MIME类型
- 没有路径问题

## 联系支持

如果您在部署过程中遇到任何问题，请联系技术支持。
