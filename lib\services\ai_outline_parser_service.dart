import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/models/novel_outline.dart';
import 'package:novel_app/services/ai_service.dart';

/// AI大纲解析服务
/// 提供AI智能解析大纲文本并转换为结构化JSON格式的功能
class AIOutlineParserService extends GetxService {
  final AIService _aiService = Get.find<AIService>();
  
  // 解析状态
  final RxBool isParsing = false.obs;
  final RxDouble parseProgress = 0.0.obs;
  final RxString parseStatus = ''.obs;
  
  /// 解析大纲文本为JSON格式
  Future<Map<String, dynamic>?> parseOutlineToJson({
    required String outlineText,
    required String novelTitle,
    void Function(String)? onProgress,
  }) async {
    if (isParsing.value) {
      print('[AIOutlineParserService] 已有解析任务在进行中');
      return null;
    }
    
    try {
      isParsing.value = true;
      parseProgress.value = 0.0;
      parseStatus.value = '正在解析大纲...';
      
      if (onProgress != null) {
        onProgress('正在解析大纲为JSON格式...');
      }
      
      final buffer = StringBuffer();
      
      await for (final chunk in _aiService.generateOutlineTextStream(
        systemPrompt: '''
你是一位专业的小说大纲解析专家。
任务：将用户提供的小说大纲文本解析为结构化的JSON格式。
''',
        userPrompt: '''
请将以下《$novelTitle》的大纲内容解析为JSON格式。应包含标题(title)和章节数组(chapters)。
每个章节应包含章节编号(chapterNumber)、章节标题(chapterTitle)和内容概要(summary)。

大纲内容：
$outlineText

请直接输出JSON，不要添加额外的解释或代码块标记。格式示例：
{
  "title": "$novelTitle",
  "chapters": [
    {
      "chapterNumber": 1,
      "chapterTitle": "章节标题",
      "summary": "章节内容概要..."
    },
    ...
  ]
}
''',
        temperature: 0.3,
        maxTokens: 4000,
        novelTitle: novelTitle,
      )) {
        buffer.write(chunk);
        parseProgress.value = 0.5; // 简单进度更新
      }
      
      final response = buffer.toString().trim();
      
      try {
        // 尝试解析JSON
        final jsonResponse = _extractJson(response);
        if (jsonResponse == null) {
          throw Exception('无法解析AI响应为JSON');
        }
        
        parseStatus.value = '解析完成！';
        parseProgress.value = 1.0;
        
        return jsonResponse;
      } catch (e) {
        print('[AIOutlineParserService] 解析AI响应失败: $e');
        print('AI响应内容: $response');
        parseStatus.value = '解析失败: $e';
        return null;
      }
    } catch (e) {
      print('[AIOutlineParserService] 解析大纲为JSON失败: $e');
      parseStatus.value = '解析失败: $e';
      return null;
    } finally {
      isParsing.value = false;
    }
  }
  
  /// 从AI响应中提取JSON
  Map<String, dynamic>? _extractJson(String text) {
    try {
      // 尝试直接解析整个文本
      return json.decode(text);
    } catch (e) {
      // 如果直接解析失败，尝试查找JSON部分
      final jsonPattern = RegExp(r'(\{.*\})', dotAll: true);
      final match = jsonPattern.firstMatch(text);
      
      if (match != null) {
        try {
          return json.decode(match.group(1)!);
        } catch (e) {
          print('提取JSON失败: $e');
        }
      }
      
      return null;
    }
  }
}
