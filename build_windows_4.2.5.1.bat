@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo Building Windows Package for Novel App v4.2.5.1
echo ===================================================

set PROJECT_DIR=D:\project\vs code\novel_app
set FLUTTER_PATH=D:\element\flutter\bin
set INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 6
set INSTALLER_SCRIPT=%PROJECT_DIR%\novel_app_setup_4.2.5.1.iss
set OUTPUT_DIR=%PROJECT_DIR%\build\windows\installer

echo Project directory: %PROJECT_DIR%
echo Flutter path: %FLUTTER_PATH%
echo Inno Setup path: %INNO_SETUP_PATH%
echo Installer script: %INSTALLER_SCRIPT%
echo Output directory: %OUTPUT_DIR%

echo.
echo Step 1: Updating version in pubspec.yaml to 4.2.5+1
echo ---------------------------------------------------
powershell -Command "(Get-Content %PROJECT_DIR%\pubspec.yaml) -replace 'version: 4.2.5', 'version: 4.2.5+1' | Set-Content %PROJECT_DIR%\pubspec.yaml"
if %ERRORLEVEL% neq 0 (
    echo Failed to update version in pubspec.yaml
    exit /b 1
)
echo Version updated successfully.

echo.
echo Step 2: Running flutter clean
echo ---------------------------------------------------
cd %PROJECT_DIR%
call %FLUTTER_PATH%\flutter clean
if %ERRORLEVEL% neq 0 (
    echo Failed to run flutter clean
    exit /b 1
)
echo Flutter clean completed successfully.

echo.
echo Step 3: Getting dependencies
echo ---------------------------------------------------
call %FLUTTER_PATH%\flutter pub get
if %ERRORLEVEL% neq 0 (
    echo Failed to get dependencies
    exit /b 1
)
echo Dependencies retrieved successfully.

echo.
echo Step 4: Building Windows Release
echo ---------------------------------------------------
call %FLUTTER_PATH%\flutter build windows --release
if %ERRORLEVEL% neq 0 (
    echo Failed to build Windows release
    exit /b 1
)
echo Windows release build completed successfully.

echo.
echo Step 5: Creating installer directory if it doesn't exist
echo ---------------------------------------------------
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"
echo Installer directory created or already exists.

echo.
echo Step 6: Building installer with Inno Setup
echo ---------------------------------------------------
"%INNO_SETUP_PATH%\ISCC.exe" "%INSTALLER_SCRIPT%"
if %ERRORLEVEL% neq 0 (
    echo Failed to build installer
    exit /b 1
)
echo Installer built successfully.

echo.
echo Step 7: Creating version.json for Windows update
echo ---------------------------------------------------
set VERSION_JSON_DIR=%PROJECT_DIR%\build\windows\installer\version
if not exist "%VERSION_JSON_DIR%" mkdir "%VERSION_JSON_DIR%"

set VERSION_JSON=%VERSION_JSON_DIR%\version.json
echo {> "%VERSION_JSON%"
echo   "version": "4.2.5",>> "%VERSION_JSON%"
echo   "buildNumber": "1",>> "%VERSION_JSON%"
echo   "releaseDate": "%date%",>> "%VERSION_JSON%"
echo   "downloadUrl": "https://dzwm.xyz/downloads/novel_app_latest.exe",>> "%VERSION_JSON%"
echo   "releaseNotes": "1. Fixed chapter generation coherence issues\n2. Optimized embedding model functionality\n3. Added version update feature\n4. Improved UI interface\n5. Enhanced AI editing functionality\n6. Fixed known bugs\n7. Added platform-specific update packages",>> "%VERSION_JSON%"
echo   "forceUpdate": false>> "%VERSION_JSON%"
echo }>> "%VERSION_JSON%"
echo Version.json created successfully.

echo.
echo ===================================================
echo Build completed successfully!
echo ===================================================
echo.
echo Installer location: %OUTPUT_DIR%\DaiZhong_Novel_Setup_4.2.5.1.exe
echo Version.json location: %VERSION_JSON%
echo.
echo Please upload these files to your server.
echo.

endlocal
