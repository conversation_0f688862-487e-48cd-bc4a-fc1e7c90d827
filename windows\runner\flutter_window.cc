#include "flutter_window.h"

#include <optional>

#include "flutter/generated_plugin_registrant.h"

// 添加Windows启动动画相关头文件
#include <windows.h>
#include <thread>
#include <chrono>

FlutterWindow::FlutterWindow(const flutter::DartProject& project)
    : project_(project) {}

FlutterWindow::~FlutterWindow() {}

bool FlutterWindow::OnCreate() {
  if (!Win32Window::OnCreate()) {
    return false;
  }

  // 显示启动动画
  ShowSplashScreen();

  RECT frame = GetClientArea();
  // The size here must match the window dimensions to avoid unnecessary surface
  // creation / destruction in the startup path.
  flutter_controller_ = std::make_unique<flutter::FlutterViewController>(
      frame.right - frame.left, frame.bottom - frame.top, project_);
  // Ensure that basic setup of the controller was successful.
  if (!flutter_controller_->engine() || !flutter_controller_->view()) {
    return false;
  }
  RegisterPlugins(flutter_controller_->engine());
  SetChildContent(flutter_controller_->view()->GetNativeWindow());

  flutter_controller_->engine()->SetNextFrameCallback([&]() {
    this->Show();
  });

  // Flutter can complete the first frame before the "show window" callback is
  // registered. The following call ensures a frame is pending to ensure the
  // window is shown. It is a no-op if the first frame hasn't completed yet.
  flutter_controller_->ForceRedraw();

  return true;
}

void FlutterWindow::OnDestroy() {
  if (flutter_controller_) {
    flutter_controller_ = nullptr;
  }

  Win32Window::OnDestroy();
}

LRESULT
FlutterWindow::MessageHandler(HWND hwnd, UINT const message,
                              WPARAM const wparam,
                              LPARAM const lparam) noexcept {
  // Give Flutter, including plugins, an opportunity to handle window messages.
  if (flutter_controller_) {
    std::optional<LRESULT> result =
        flutter_controller_->HandleTopLevelWindowProc(hwnd, message, wparam,
                                                      lparam);
    if (result) {
      return *result;
    }
  }

  switch (message) {
    case WM_FONTCHANGE:
      flutter_controller_->engine()->ReloadSystemFonts();
      break;
  }

  return Win32Window::MessageHandler(hwnd, message, wparam, lparam);
}

// 显示Windows启动动画
void FlutterWindow::ShowSplashScreen() {
  // 获取窗口句柄
  HWND hwnd = GetHandle();
  if (!hwnd) {
    return;
  }

  // 获取客户区域
  RECT rect;
  GetClientRect(hwnd, &rect);
  int width = rect.right - rect.left;
  int height = rect.bottom - rect.top;

  // 创建设备上下文
  HDC hdc = GetDC(hwnd);
  HDC memDC = CreateCompatibleDC(hdc);
  HBITMAP memBitmap = CreateCompatibleBitmap(hdc, width, height);
  SelectObject(memDC, memBitmap);

  // 绘制背景 - 使用岱宗文脉主题色
  HBRUSH bgBrush = CreateSolidBrush(RGB(58, 107, 53)); // #3A6B35
  FillRect(memDC, &rect, bgBrush);
  DeleteObject(bgBrush);

  // 绘制应用名称
  SetBkMode(memDC, TRANSPARENT);
  SetTextColor(memDC, RGB(255, 255, 255));
  
  HFONT titleFont = CreateFont(
      36, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE,
      DEFAULT_CHARSET, OUT_OUTLINE_PRECIS, CLIP_DEFAULT_PRECIS,
      CLEARTYPE_QUALITY, DEFAULT_PITCH | FF_DONTCARE, L"Microsoft YaHei");
  
  SelectObject(memDC, titleFont);
  
  const wchar_t* title = L"岱宗文脉";
  SIZE titleSize;
  GetTextExtentPoint32W(memDC, title, wcslen(title), &titleSize);
  TextOutW(memDC, (width - titleSize.cx) / 2, height / 2 - 50, title, wcslen(title));
  
  DeleteObject(titleFont);

  // 绘制标语
  HFONT sloganFont = CreateFont(
      20, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
      DEFAULT_CHARSET, OUT_OUTLINE_PRECIS, CLIP_DEFAULT_PRECIS,
      CLEARTYPE_QUALITY, DEFAULT_PITCH | FF_DONTCARE, L"Microsoft YaHei");
  
  SelectObject(memDC, sloganFont);
  
  const wchar_t* slogan = L"汲取泰山灵气，承载文脉传承";
  SIZE sloganSize;
  GetTextExtentPoint32W(memDC, slogan, wcslen(slogan), &sloganSize);
  TextOutW(memDC, (width - sloganSize.cx) / 2, height / 2, slogan, wcslen(slogan));
  
  DeleteObject(sloganFont);

  // 绘制加载进度条
  int progressBarWidth = 300;
  int progressBarHeight = 6;
  int progressBarX = (width - progressBarWidth) / 2;
  int progressBarY = height / 2 + 50;
  
  // 进度条背景
  HBRUSH progressBgBrush = CreateSolidBrush(RGB(147, 184, 132)); // #93B884
  RECT progressBgRect = {progressBarX, progressBarY, 
                         progressBarX + progressBarWidth, 
                         progressBarY + progressBarHeight};
  FillRect(memDC, &progressBgRect, progressBgBrush);
  DeleteObject(progressBgBrush);

  // 将内存DC的内容复制到窗口DC
  BitBlt(hdc, 0, 0, width, height, memDC, 0, 0, SRCCOPY);

  // 模拟加载进度
  HBRUSH progressFgBrush = CreateSolidBrush(RGB(242, 227, 188)); // #F2E3BC
  for (int i = 0; i <= 100; i += 5) {
    int progressWidth = (progressBarWidth * i) / 100;
    RECT progressRect = {progressBarX, progressBarY, 
                         progressBarX + progressWidth, 
                         progressBarY + progressBarHeight};
    FillRect(memDC, &progressRect, progressFgBrush);
    BitBlt(hdc, 0, 0, width, height, memDC, 0, 0, SRCCOPY);
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
  }
  DeleteObject(progressFgBrush);

  // 清理资源
  DeleteObject(memBitmap);
  DeleteDC(memDC);
  ReleaseDC(hwnd, hdc);
}
