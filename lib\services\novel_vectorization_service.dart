import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/controllers/writing_style_package_controller.dart';
import 'package:novel_app/services/embedding_service.dart';
import 'package:novel_app/langchain/models/novel_memory.dart';
import 'package:novel_app/utils/text_splitter.dart';
import 'package:hive/hive.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:math';

/// 小说内容向量化服务
/// 用于将小说内容分段并向量化存储，以便进行语义检索
class NovelVectorizationService extends GetxService {
  static const String _boxName = 'novel_vectors';
  late Box<Map> _box;

  final ApiConfigController _apiConfigController;
  final EmbeddingService _embeddingService;

  // 向量化状态
  final RxBool isVectorizing = false.obs;
  final RxDouble vectorizationProgress = 0.0.obs;
  final RxString vectorizationStatus = ''.obs;

  // 缓存，避免重复向量化
  final Map<String, bool> _vectorizedNovels = {};

  NovelVectorizationService({
    required ApiConfigController apiConfigController,
    required EmbeddingService embeddingService,
  })  : _apiConfigController = apiConfigController,
        _embeddingService = embeddingService;

  /// 初始化服务
  Future<NovelVectorizationService> init() async {
    try {
      // Web平台不需要初始化Hive路径，已在main.dart中完成
      if (!kIsWeb) {
        // 非Web平台的初始化代码已在main.dart中完成
      }

      // 打开Box
      _box = await Hive.openBox<Map>(_boxName);

      print('[NovelVectorizationService] 初始化成功，已加载 ${_box.length} 条向量记录');
      return this;
    } catch (e) {
      print('[NovelVectorizationService] 初始化失败: $e');
      // 创建一个内存中的Box作为备用
      _box = await Hive.openBox<Map>(_boxName, path: '');
      return this;
    }
  }

  /// 检查小说是否已向量化
  bool isNovelVectorized(String novelTitle) {
    // 先检查缓存
    if (_vectorizedNovels.containsKey(novelTitle)) {
      return _vectorizedNovels[novelTitle]!;
    }

    // 检查Hive中是否有该小说的向量
    final novelKey = _getNovelKey(novelTitle);
    final hasVectors = _box.containsKey(novelKey) &&
        (_box.get(novelKey) as Map?)?.isNotEmpty == true;

    // 更新缓存
    _vectorizedNovels[novelTitle] = hasVectors;

    return hasVectors;
  }

  /// 向量化小说内容
  /// 将小说内容分段并向量化存储
  Future<bool> vectorizeNovel(String novelTitle) async {
    if (!_apiConfigController.embeddingModel.value.enabled) {
      print('[NovelVectorizationService] 嵌入模型未启用，无法向量化');
      return false;
    }

    if (isVectorizing.value) {
      print('[NovelVectorizationService] 已有向量化任务在进行中');
      return false;
    }

    try {
      isVectorizing.value = true;
      vectorizationProgress.value = 0.0;
      vectorizationStatus.value = '正在准备向量化 "$novelTitle"...';

      // 获取小说内容
      final novelMemory = NovelMemory(novelTitle: novelTitle);

      // 获取大纲
      final outline = await novelMemory.getOutline();
      if (outline == null || outline.isEmpty) {
        print('[NovelVectorizationService] 小说大纲为空，无法向量化');
        isVectorizing.value = false;
        return false;
      }

      // 获取所有章节
      final chapters = await novelMemory.getAllChapters();
      if (chapters.isEmpty) {
        print('[NovelVectorizationService] 小说章节为空，无法向量化');
        isVectorizing.value = false;
        return false;
      }

      // 准备向量化数据
      final List<Map<String, dynamic>> segments = [];

      // 添加大纲
      segments.add({
        'type': 'outline',
        'content': outline,
        'title': '大纲',
        'chapter': 0,
      });

      // 添加章节
      for (final entry in chapters.entries) {
        final chapterNumber = entry.key;
        final chapterContent = entry.value;
        final chapterTitle = await novelMemory.getChapterTitle(chapterNumber) ??
            '第$chapterNumber章';

        // 将章节分段，每段约500-1000字
        final textSegments = _segmentText(chapterContent, 800);

        // 将文本段转换为带有元数据的段落
        for (int i = 0; i < textSegments.length; i++) {
          final segment = textSegments[i];
          segments.add({
            'type': 'chapter',
            'content': segment,
            'title': chapterTitle,
            'chapter': chapterNumber,
            'segment': i + 1,
          });
        }

        // 更新进度
        vectorizationProgress.value = chapters.entries.toList().indexOf(entry) /
            chapters.length *
            0.4; // 调整进度比例
        vectorizationStatus.value = '正在处理第$chapterNumber章...';
      }

      // 添加知识库内容（如果有）
      final knowledgeBaseController = Get.find<KnowledgeBaseController>();
      if (knowledgeBaseController.useKnowledgeBase.value &&
          knowledgeBaseController.selectedDocIds.isNotEmpty) {
        vectorizationStatus.value = '正在处理知识库内容...';

        final selectedDocs = knowledgeBaseController.getSelectedDocuments();

        for (int docIndex = 0; docIndex < selectedDocs.length; docIndex++) {
          final doc = selectedDocs[docIndex];

          // 将知识库文档分段
          final docSegments = _segmentText(doc.content, 800);

          // 将文本段转换为带有元数据的段落
          for (int i = 0; i < docSegments.length; i++) {
            final segment = docSegments[i];
            segments.add({
              'type': 'knowledge',
              'content': segment,
              'title': doc.title,
              'category': doc.category,
              'segment': i + 1,
            });
          }

          // 更新进度
          vectorizationProgress.value =
              0.4 + (docIndex / selectedDocs.length * 0.05);
        }

        vectorizationStatus.value = '知识库内容处理完成';
      }

      // 添加文风包内容（如果有）
      try {
        // 获取当前使用的文风包
        final writingStylePackageController =
            Get.find<WritingStylePackageController>();
        final currentPackage =
            writingStylePackageController.currentPackage.value;

        if (currentPackage != null) {
          vectorizationStatus.value = '正在处理文风包内容...';

          // 构建文风包内容
          final buffer = StringBuffer();
          buffer.writeln('# 文风包: ${currentPackage.name}');
          if (currentPackage.author.isNotEmpty)
            buffer.writeln('作者: ${currentPackage.author}');
          if (currentPackage.description.isNotEmpty)
            buffer.writeln('风格描述: ${currentPackage.description}');

          // 添加示例文本
          if (currentPackage.sampleTexts.isNotEmpty) {
            buffer.writeln('示例文本:');
            for (final sample in currentPackage.sampleTexts) {
              buffer.writeln(sample);
              buffer.writeln('---');
            }
          }

          final styleContent = buffer.toString();

          // 将文风包内容分段
          final styleSegments = _segmentText(styleContent, 800);

          // 将文本段转换为带有元数据的段落
          for (int i = 0; i < styleSegments.length; i++) {
            final segment = styleSegments[i];
            segments.add({
              'type': 'writing_style',
              'content': segment,
              'title': '文风: ${currentPackage.name}',
              'segment': i + 1,
            });
          }

          vectorizationStatus.value = '文风包内容处理完成';
          vectorizationProgress.value = 0.45;
        }
      } catch (e) {
        // 如果获取文风包失败，记录错误但继续执行
        print('[NovelVectorizationService] 处理文风包时出错: $e');
      }

      print('[NovelVectorizationService] 共分段 ${segments.length} 个片段');
      vectorizationStatus.value = '正在向量化 ${segments.length} 个片段...';

      // 向量化所有片段
      final List<Map<String, dynamic>> vectorizedSegments = [];

      for (int i = 0; i < segments.length; i++) {
        final segment = segments[i];
        final content = segment['content'] as String;

        try {
          // 获取嵌入向量
          final vector = await _embeddingService.getEmbedding(content);

          // 添加向量化结果
          vectorizedSegments.add({
            ...segment,
            'vector': vector,
          });

          // 更新进度
          vectorizationProgress.value = 0.5 + (i / segments.length * 0.5);
          vectorizationStatus.value = '已向量化 ${i + 1}/${segments.length} 个片段...';
        } catch (e) {
          print('[NovelVectorizationService] 向量化片段失败: $e');
          // 继续处理下一个片段
        }
      }

      // 保存向量化结果
      await _saveVectorizedSegments(novelTitle, vectorizedSegments);

      // 更新缓存
      _vectorizedNovels[novelTitle] = true;

      vectorizationProgress.value = 1.0;
      vectorizationStatus.value = '向量化完成，共处理 ${vectorizedSegments.length} 个片段';

      print(
          '[NovelVectorizationService] 向量化完成，共处理 ${vectorizedSegments.length} 个片段');
      return true;
    } catch (e) {
      print('[NovelVectorizationService] 向量化失败: $e');
      vectorizationStatus.value = '向量化失败: $e';
      return false;
    } finally {
      isVectorizing.value = false;
    }
  }

  /// 向量化单个章节内容
  /// 将章节内容分段并向量化存储，用于新生成章节的即时向量化
  Future<bool> vectorizeChapter({
    required String novelTitle,
    required int chapterNumber,
    required String chapterTitle,
    required String content,
  }) async {
    if (!_apiConfigController.embeddingModel.value.enabled) {
      print('[NovelVectorizationService] 嵌入模型未启用，无法向量化章节');
      return false;
    }

    try {
      // 检查小说是否已向量化，如果没有则先向量化整本小说
      if (!isNovelVectorized(novelTitle)) {
        print('[NovelVectorizationService] 小说未向量化，先进行完整向量化');
        final result = await vectorizeNovel(novelTitle);
        if (!result) {
          print('[NovelVectorizationService] 小说向量化失败，无法向量化章节');
          return false;
        }
      }

      // 获取现有向量化片段
      final existingSegments = await _getVectorizedSegments(novelTitle);

      // 将章节分段
      final textSegments = _segmentText(content, 800);

      // 准备新的向量化片段
      final List<Map<String, dynamic>> newSegments = [];

      for (int i = 0; i < textSegments.length; i++) {
        final segment = textSegments[i];

        try {
          // 获取嵌入向量
          final vector = await _embeddingService.getEmbedding(segment);

          // 添加向量化结果
          newSegments.add({
            'type': 'chapter',
            'content': segment,
            'title': chapterTitle,
            'chapter': chapterNumber,
            'segment': i + 1,
            'vector': vector,
          });
        } catch (e) {
          print('[NovelVectorizationService] 向量化章节片段失败: $e');
          // 继续处理下一个片段
        }
      }

      if (newSegments.isEmpty) {
        print('[NovelVectorizationService] 章节向量化失败，没有成功向量化的片段');
        return false;
      }

      // 合并现有片段和新片段
      final allSegments = [...existingSegments];

      // 移除同一章节的旧片段（如果有）
      allSegments.removeWhere((segment) =>
          segment['type'] == 'chapter' && segment['chapter'] == chapterNumber);

      // 添加新片段
      allSegments.addAll(newSegments);

      // 保存更新后的向量化片段
      await _saveVectorizedSegments(novelTitle, allSegments);

      print(
          '[NovelVectorizationService] 章节向量化成功，添加了 ${newSegments.length} 个片段');
      return true;
    } catch (e) {
      print('[NovelVectorizationService] 章节向量化失败: $e');
      return false;
    }
  }

  /// 检索与查询文本最相关的小说内容
  ///
  /// 参数:
  /// - novelTitle: 小说标题
  /// - query: 查询文本
  /// - maxResults: 最大返回结果数
  /// - includeKnowledgeBase: 是否包含知识库内容
  /// - includeWritingStyle: 是否包含文风包内容
  Future<List<Map<String, dynamic>>> searchNovelContent(
      String novelTitle, String query,
      {int maxResults = 5,
      bool includeKnowledgeBase = false,
      bool includeWritingStyle = false}) async {
    if (!_apiConfigController.embeddingModel.value.enabled) {
      print('[NovelVectorizationService] 嵌入模型未启用，无法检索');
      return [];
    }

    if (!isNovelVectorized(novelTitle)) {
      print('[NovelVectorizationService] 小说未向量化，无法检索');
      return [];
    }

    try {
      // 获取查询向量
      final queryVector = await _embeddingService.getEmbedding(query);

      // 获取小说向量化片段
      final segments = await _getVectorizedSegments(novelTitle);
      if (segments.isEmpty) {
        print('[NovelVectorizationService] 未找到向量化片段');
        return [];
      }

      // 计算相似度并排序
      final results = <Map<String, dynamic>>[];

      for (final segment in segments) {
        // 根据类型过滤片段
        final type = segment['type'] as String;

        // 如果是知识库内容，但不包含知识库，则跳过
        if (type == 'knowledge' && !includeKnowledgeBase) {
          continue;
        }

        // 如果是文风包内容，但不包含文风包，则跳过
        if (type == 'writing_style' && !includeWritingStyle) {
          continue;
        }

        final vector = segment['vector'] as List<double>;
        final similarity = _cosineSimilarity(queryVector, vector);

        results.add({
          'content': segment['content'],
          'title': segment['title'],
          'chapter': segment['chapter'],
          'segment': segment['segment'] ?? 0,
          'type': type,
          'similarity': similarity,
        });
      }

      // 按相似度排序
      results.sort((a, b) =>
          (b['similarity'] as double).compareTo(a['similarity'] as double));

      // 返回前N个结果
      return results.take(maxResults).toList();
    } catch (e) {
      print('[NovelVectorizationService] 检索失败: $e');
      return [];
    }
  }

  /// 将文本分段
  ///
  /// 公开方法，供其他服务使用
  List<String> segmentText(String text, int targetLength) {
    final config = _apiConfigController.embeddingModel.value;

    // 使用TextSplitter工具类进行分段
    return TextSplitter.splitForVectorization(
      text,
      maxLength: config.maxChunkSize > 0 ? config.maxChunkSize : targetLength,
      similarityThreshold: config.similarityThreshold,
    );
  }

  // 内部使用的分段方法，保持兼容性
  List<String> _segmentText(String text, int targetLength) {
    return segmentText(text, targetLength);
  }

  /// 保存向量化片段
  Future<void> _saveVectorizedSegments(
      String novelTitle, List<Map<String, dynamic>> segments) async {
    final novelKey = _getNovelKey(novelTitle);
    await _box.put(novelKey, {'segments': segments});
  }

  /// 获取向量化片段
  Future<List<Map<String, dynamic>>> _getVectorizedSegments(
      String novelTitle) async {
    final novelKey = _getNovelKey(novelTitle);
    final data = _box.get(novelKey) as Map?;

    if (data == null || !data.containsKey('segments')) {
      return [];
    }

    return (data['segments'] as List).cast<Map<String, dynamic>>();
  }

  /// 计算余弦相似度
  double _cosineSimilarity(List<double> vec1, List<double> vec2) {
    return TextSplitter.calculateCosineSimilarity(vec1, vec2);
  }

  /// 获取小说键
  String _getNovelKey(String novelTitle) {
    return 'novel_vectors_$novelTitle';
  }

  /// 清除小说向量
  Future<void> clearNovelVectors(String novelTitle) async {
    final novelKey = _getNovelKey(novelTitle);
    await _box.delete(novelKey);
    _vectorizedNovels.remove(novelTitle);
  }

  /// 清除所有向量
  Future<void> clearAllVectors() async {
    await _box.clear();
    _vectorizedNovels.clear();
  }
}
