# 使用宝塔面板部署岱宗文脉后台管理系统

本文档提供了使用宝塔面板在服务器上部署岱宗文脉后台管理系统的详细步骤，包括用户注册和登录系统。

## 1. 安装宝塔面板

### CentOS 系统安装命令

```bash
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh
```

安装完成后，宝塔面板会显示以下信息：
- 宝塔面板地址：`http://服务器IP:8888/xxxx`
- 默认用户名：`username`
- 默认密码：`password`

请记录这些信息，用于登录宝塔面板。

## 2. 面板初始化设置

1. 使用浏览器访问宝塔面板地址
2. 使用默认用户名和密码登录
3. 根据引导完成初始化设置
4. 在软件商店中安装以下组件：
   - Nginx（推荐1.18或更高版本）
   - Python项目管理器
   - PostgreSQL（12或更高版本）
   - PM2管理器（可选，用于管理Node.js应用）
   - Redis（可选，用于缓存）

## 3. 创建PostgreSQL数据库

1. 在宝塔面板左侧菜单中点击【数据库】
2. 选择【PostgreSQL】选项卡
3. 点击【添加数据库】
4. 填写以下信息：
   - 数据库名：`novel`
   - 用户名：`novel_user`
   - 密码：设置一个安全的密码
   - 访问权限：本地服务器
5. 点击【提交】创建数据库

### 授予数据库用户额外权限

创建数据库后，需要授予用户在public模式中创建表的权限：

1. 在宝塔面板左侧菜单中点击【终端】
2. 执行以下命令登录PostgreSQL：

```bash
sudo -u postgres psql
```

3. 执行以下SQL命令授予权限：

```sql
-- 授予novel_user在public模式上的所有权限
GRANT ALL ON SCHEMA public TO novel_user;

-- 授予novel_user在novel数据库中创建表的权限
\c novel
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO novel_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO novel_user;

-- 退出PostgreSQL
\q
```

## 4. 上传项目文件

1. 在宝塔面板左侧菜单中点击【文件】
2. 导航到 `/www/wwwroot/` 目录
3. 点击【上传】按钮，上传项目文件或使用以下方法：

```bash
# 通过SSH连接到服务器
cd /www/wwwroot/
mkdir novel_app
cd novel_app

# 使用Git克隆项目（如果使用Git）
git clone https://github.com/yourusername/novel_app.git .

# 或者使用宝塔面板的文件管理器上传项目文件
```

## 5. 配置环境变量

1. 在宝塔面板的文件管理器中，导航到项目的backend目录
2. 复制`.env.example`文件并重命名为`.env`
3. 编辑`.env`文件，配置以下内容：

```
# 数据库配置
DATABASE_URL=postgresql://novel_user:your_secure_password@localhost/novel

# JWT配置
SECRET_KEY=your_random_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure_admin_password
ADMIN_USERNAME=admin
```

请确保替换`your_secure_password`为您在步骤3中设置的数据库密码，并为`SECRET_KEY`设置一个随机的安全密钥。

## 6. 创建Python项目

1. 在宝塔面板左侧菜单中点击【Python项目】
2. 点击【添加项目】
3. 填写以下信息：
   - 项目名称：`novel_app`
   - 路径：`/www/wwwroot/novel_app/backend`
   - Python版本：选择3.9或更高版本
   - 框架：FastAPI
   - 启动方式：python（从下拉菜单中选择）
   - 启动文件名：`run.py`
   - 端口：`8000`
4. 点击【提交】创建项目

> **注意**：如果下拉菜单中有"gunicorn"或"uwsgi"选项，也可以选择这些作为启动方式，它们在生产环境中可能提供更好的性能。

## 7. 安装项目依赖

1. 在Python项目管理器中找到项目
2. 点击【模块】
3. 点击【从文件安装】
4. 选择项目目录中的 `requirements.txt` 文件
5. 等待依赖安装完成

或者通过终端安装：

```bash
cd /www/wwwroot/novel_app/backend
pip3 install -r requirements.txt
```

## 8. 初始化数据库

1. 在宝塔面板左侧菜单中点击【终端】
2. 执行以下命令：

```bash
cd /www/wwwroot/novel_app/backend

# 创建迁移目录
mkdir -p alembic/versions

# 初始化数据库表
python -m alembic upgrade head

# 创建初始管理员用户
python init_db.py
```

## 9. 配置网站

1. 在宝塔面板左侧菜单中点击【网站】
2. 点击【添加站点】
3. 填写以下信息：
   - 域名：`api.dzwm.xyz`（替换为您的域名）
   - 备注：岱宗文脉API
   - 根目录：`/www/wwwroot/novel_app/backend`
   - PHP版本：纯静态
   - 数据库：不创建
4. 点击【提交】创建站点

## 10. 配置反向代理

1. 在网站列表中找到刚创建的站点
2. 点击【设置】
3. 在左侧菜单中点击【反向代理】
4. 点击【添加反向代理】
5. 填写以下信息：
   - 代理名称：novel_app_api
   - 目标URL：`http://127.0.0.1:8000`
   - 发送域名：$host
6. 点击【提交】

## 11. 启动项目

1. 在宝塔面板左侧菜单中点击【Python项目】
2. 找到`novel_app`项目
3. 点击【启动】按钮
4. 查看运行状态和日志，确保项目正常运行

如果项目无法启动，可以尝试以下方法：

1. 检查日志输出，查找错误信息
2. 确认所有依赖已正确安装
3. 尝试手动启动项目：
   ```bash
   cd /www/wwwroot/novel_app/backend
   python run.py
   ```
4. 如果手动启动成功但宝塔面板无法启动，可以尝试修改启动方式或使用自定义启动命令

## 12. 配置SSL证书（推荐）

1. 在宝塔面板左侧菜单中点击【网站】
2. 找到您的站点，点击【设置】
3. 在左侧菜单中点击【SSL】
4. 选择【Let's Encrypt】选项卡
5. 勾选需要申请证书的域名
6. 点击【申请】
7. 证书申请成功后，点击【强制HTTPS】

## 13. 设置定时备份

1. 在宝塔面板左侧菜单中点击【计划任务】
2. 点击【添加计划任务】
3. 填写以下信息：
   - 任务名称：备份novel数据库
   - 执行周期：每天
   - 执行时间：凌晨3点
   - 脚本内容：

```bash
#!/bin/bash
BACKUP_DIR="/www/backup/database/novel"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/novel_$TIMESTAMP.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
/www/server/postgresql/bin/pg_dump -U novel_user novel > $BACKUP_FILE

# 压缩备份
gzip $BACKUP_FILE

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -type f -mtime +7 -delete
```

4. 点击【提交】

## 14. 更新项目

当需要更新项目时，请按照以下步骤操作：

1. 在宝塔面板左侧菜单中点击【Python项目】
2. 找到`novel_app`项目，点击【停止】
3. 在文件管理器中更新项目文件（上传新文件或使用Git拉取更新）
4. 在终端中执行以下命令：

```bash
cd /www/wwwroot/novel_app/backend

# 安装新依赖
pip3 install -r requirements.txt

# 应用数据库迁移
python -m alembic upgrade head

# 更新管理员用户
python init_db.py
```

5. 在Python项目管理器中重新启动项目

> **提示**：如果您在宝塔面板中遇到Python项目管理的问题，也可以考虑使用宝塔面板的"PM2管理器"来管理Python项目。PM2虽然主要用于Node.js应用，但也可以用来管理Python应用。

## 15. 故障排除

### 常见问题

1. **项目无法启动**：
   - 检查Python项目日志
   - 确认所有依赖已正确安装
   - 检查环境变量配置
   - 尝试使用不同的启动方式（python、gunicorn、uwsgi）
   - 检查启动文件路径是否正确

2. **数据库连接错误**：
   - 确认PostgreSQL服务正在运行
   - 检查数据库连接配置
   - 确认数据库用户权限正确
   - 确认数据库名称为`novel`而非`novel_app`
   - 如果遇到"permission denied for schema public"错误，需要执行以下命令授予权限：
     ```sql
     sudo -u postgres psql
     GRANT ALL ON SCHEMA public TO novel_user;
     \c novel
     ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO novel_user;
     ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO novel_user;
     \q
     ```

3. **Nginx 502 错误**：
   - 检查Python项目是否正在运行
   - 检查反向代理配置
   - 检查端口是否被占用
   - 查看Nginx错误日志：`/www/wwwlogs/nginx_error.log`

4. **SSL证书问题**：
   - 确认域名DNS解析正确
   - 检查证书是否过期
   - 尝试重新申请证书

5. **宝塔面板Python项目管理问题**：
   - 如果宝塔面板的Python项目管理器无法正常工作，可以尝试使用PM2或Supervisor
   - 在宝塔面板中安装"Supervisor管理器"插件
   - 使用Supervisor创建一个新的进程来运行Python应用

如有其他问题，请查看宝塔面板的日志和系统日志以获取更多信息。

## 16. 安全建议

1. 修改宝塔面板默认端口
2. 启用宝塔面板防火墙
3. 定期更新系统和软件包
4. 定期备份数据库和项目文件
5. 使用强密码并定期更换
6. 限制SSH访问IP
7. 配置防火墙只开放必要端口

## 17. 使用Supervisor管理Python应用（备选方案）

如果宝塔面板的Python项目管理器不能满足需求，可以使用Supervisor来管理Python应用：

1. 在宝塔面板中安装"Supervisor管理器"插件
2. 点击【添加守护进程】
3. 填写以下信息：
   - 名称：`novel_app`
   - 启动用户：`www`（或其他有权限的用户）
   - 运行目录：`/www/wwwroot/novel_app/backend`
   - 启动命令：`python run.py`
   - 进程数：`1`
4. 点击【确定】创建守护进程
5. 使用Supervisor启动和管理应用

这种方式可能比宝塔面板的Python项目管理器更加稳定和灵活。
