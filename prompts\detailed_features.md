# AI小说生成器详细功能设计

## 1. 续写功能实现

### 1.1 续写Chain设计
```python
class NovelContinuationChain:
    """
    输入参数:
    - 已有小说内容
    - 续写章节数
    - 续写提示
    - 历史上下文
    - 角色信息
    - 写作风格
    
    工作流程:
    1. 分析已有内容
    2. 提取关键情节线
    3. 生成续写大纲
    4. 章节详细生成
    """
```

### 1.2 续写提示词模板
```
你是专业的小说续写助手，请基于以下信息继续创作故事：

【现有内容分析】
小说标题：{title}
已有章节：{existing_chapters}
主要情节：{main_plots}
人物关系：{character_relationships}
世界设定：{world_settings}

【续写要求】
续写章节数：{continue_chapters}
特殊要求：{special_requirements}
写作风格：{writing_style}

【续写指南】
1. 保持人物性格连贯性
2. 延续已有的情节线索
3. 符合世界设定规则
4. 避免情节重复或倒退
5. 合理设置新的悬念和冲突

请按照以下格式输出续写内容：
第X章：章节标题
章节内容...
```

## 2. 短篇小说功能

### 2.1 短篇小说Chain
```python
class ShortNovelChain:
    """
    配置参数:
    - 字数限制（默认15000字）
    - 结构模板选择
    - 节奏控制
    
    输出要求:
    - 完整故事结构
    - 紧凑情节发展
    - 有效的人物刻画
    - 主题鲜明
    """
```

### 2.2 短篇小说模板库
```python
SHORT_NOVEL_TEMPLATES = {
    "标准三幕式": {
        "结构": ["开端(20%)", "发展(60%)", "结局(20%)"],
        "关键点": ["引子", "第一转折", "危机", "高潮", "结局"]
    },
    "倒叙结构": {
        "结构": ["现在(10%)", "回忆(70%)", "现实(20%)"],
        "关键点": ["悬念设置", "背景铺垫", "真相揭示", "情感升华"]
    },
    "环形结构": {
        "结构": ["首尾呼应(15%)", "主体(70%)", "首尾呼应(15%)"],
        "关键点": ["开篇意象", "情节推进", "意象重现", "主题升华"]
    }
}
```

### 2.3 男性向短篇特化
```python
MALE_SHORT_NOVEL_FEATURES = {
    "叙事特点": [
        "快速切入主题",
        "强调冲突和动作",
        "简洁有力的对话",
        "克制的感情描写",
        "注重逻辑和推理"
    ],
    "情节设计": [
        "设置明确的对抗关系",
        "包含技术或专业元素",
        "适度的悬念设置",
        "清晰的能力体系",
        "合理的解决方案"
    ]
}
```

## 3. 小说类型系统

### 3.1 类型定义模板
```python
class NovelGenre:
    """
    属性定义：
    - 类型名称
    - 核心特征
    - 常见元素
    - 情节模式
    - 人物原型
    - 世界设定
    - 写作技巧
    """
```

### 3.2 预设类型库
```python
NOVEL_GENRES = {
    "玄幻": {
        "核心特征": [
            "独特的修炼体系",
            "超凡能力设定",
            "宏大世界观",
            "等级制度"
        ],
        "情节模式": [
            "修炼突破",
            "势力冲突",
            "机缘际遇",
            "征战四方"
        ],
        "写作要点": [
            "设计合理的修炼体系",
            "构建完整的世界观",
            "安排循序渐进的成长路线"
        ]
    },
    "科幻": {
        "核心特征": [
            "科学基础",
            "未来科技",
            "社会影响",
            "伦理思考"
        ],
        "情节模式": [
            "科技突破",
            "文明冲突",
            "时空探索",
            "人性考验"
        ],
        "写作要点": [
            "科学原理的合理运用",
            "未来社会的逻辑构建",
            "科技与人性的平衡"
        ]
    },
    # ... 其他类型
}
```

## 4. 写作风格系统

### 4.1 风格包结构
```python
class WritingStylePackage:
    """
    包含元素：
    1. 基本信息：
       - 风格名称
       - 风格描述
       - 适用类型
       - 目标读者
    
    2. 具体规范：
       - 句式特点
       - 用词偏好
       - 描写方式
       - 节奏控制
       - 对话特点
    
    3. 示例片段：
       - 场景描写示例
       - 动作描写示例
       - 对话示例
       - 心理描写示例
    """
```

### 4.2 预设风格示例
```python
WRITING_STYLES = {
    "轻松幽默": {
        "语言特点": [
            "口语化表达",
            "夸张比喻",
            "俏皮对话",
            "轻快节奏"
        ],
        "描写倾向": [
            "简洁生动",
            "突出趣味性",
            "善用反差效果"
        ],
        "示例模板": """
        [场景示例]
        阳光明媚的午后，小区的健身器材区突然变成了老年人的欢乐谷。
        王大爷在单杠上倒挂金钟，那姿势，简直比杂技演员还专业。
        
        [对话示例]
        "老王，你这是修炼成精了啊！"李大爷在旁边惊叹道。
        "嘿嘿，这都是基本操作，"王大爷得意地说，"要不是腰间盘突出，
        我能给你来个后空翻。"
        """
    }
    # ... 其他风格
}
```

## 5. 角色管理系统

### 5.1 角色类型定义
```python
class CharacterType:
    """
    属性定义：
    - 类型ID
    - 类型名称
    - 典型特征
    - 行为模式
    - 成长路线
    - 关系定位
    """
    
    def get_prompt_template(self) -> str:
        """返回该类型的角色生成提示模板"""
        pass
```

### 5.2 角色卡片系统
```python
class CharacterCard:
    """
    基础信息：
    - 角色ID
    - 角色名称
    - 性别年龄
    - 外貌特征
    - 性格特点
    
    深度设定：
    - 成长背景
    - 核心动机
    - 价值观念
    - 能力特长
    - 关系网络
    
    动态属性：
    - 情感状态
    - 目标进展
    - 关系变化
    - 能力成长
    """
```

### 5.3 角色生成器
```python
class CharacterGenerator:
    """
    生成功能：
    1. 基于类型生成完整角色
    2. 生成角色关系网络
    3. 生成角色成长轨迹
    4. 生成角色对话风格
    """
    
    async def generate_character(self,
        character_type: CharacterType,
        novel_context: Dict,
        special_requirements: Dict = None
    ) -> CharacterCard:
        """生成符合特定类型的角色"""
        pass
```

### 5.4 预设角色类型库
```python
CHARACTER_TYPES = {
    "主角": {
        "特征": [
            "鲜明的个性",
            "明确的目标",
            "成长潜力",
            "关键能力"
        ],
        "行为模式": [
            "主动推动剧情",
            "面对挑战时的选择",
            "与其他角色的互动"
        ],
        "成长路线": [
            "能力提升",
            "性格塑造",
            "关系发展"
        ]
    },
    "反派": {
        "特征": [
            "明确的对抗性",
            "独特的价值观",
            "强大的能力",
            "复杂的动机"
        ],
        "行为模式": [
            "制造冲突",
            "阻碍主角",
            "展现智谋"
        ],
        "成长路线": [
            "实力增长",
            "计划推进",
            "最终对决"
        ]
    }
    # ... 其他类型
}
```

## 6. 数据模型补充

### 6.1 续写记录模型
```python
class ContinuationRecord:
    id: str
    novel_id: str
    original_chapters: List[int]
    new_chapters: List[int]
    continuation_prompt: str
    created_at: datetime
    metadata: Dict
```

### 6.2 短篇小说模型
```python
class ShortNovel:
    id: str
    title: str
    content: str
    word_count: int
    structure_type: str
    writing_style: str
    characters: List[CharacterCard]
    metadata: Dict
```

### 6.3 风格包模型
```python
class StylePackage:
    id: str
    name: str
    description: str
    rules: Dict
    examples: Dict
    compatible_genres: List[str]
    metadata: Dict
```

## 7. API接口补充

### 7.1 续写相关接口
```python
@router.post("/api/v1/novel/{novel_id}/continue")
async def continue_novel(
    novel_id: str,
    continuation_params: ContinuationParams
) -> ContinuationResponse:
    """小说续写接口"""
    pass

@router.get("/api/v1/novel/{novel_id}/continuation-history")
async def get_continuation_history(
    novel_id: str
) -> List[ContinuationRecord]:
    """获取续写历史"""
    pass
```

### 7.2 短篇小说接口
```python
@router.post("/api/v1/short-novel")
async def create_short_novel(
    params: ShortNovelParams
) -> ShortNovel:
    """创建短篇小说"""
    pass

@router.get("/api/v1/short-novel/templates")
async def get_short_novel_templates() -> Dict:
    """获取短篇小说模板"""
    pass
```

### 7.3 角色管理接口
```python
@router.post("/api/v1/character-types")
async def create_character_type(
    type_data: CharacterTypeCreate
) -> CharacterType:
    """创建角色类型"""
    pass

@router.post("/api/v1/character-cards")
async def create_character_card(
    card_data: CharacterCardCreate
) -> CharacterCard:
    """创建角色卡片"""
    pass

@router.post("/api/v1/character-generator")
async def generate_character(
    params: CharacterGenerationParams
) -> CharacterCard:
    """生成角色"""
    pass
```