import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/services/update_service.dart';
import 'package:package_info_plus/package_info_plus.dart';

class UpdateScreen extends StatefulWidget {
  const UpdateScreen({super.key});

  @override
  State<UpdateScreen> createState() => _UpdateScreenState();
}

class _UpdateScreenState extends State<UpdateScreen> {
  final UpdateService _updateService = Get.find<UpdateService>();
  PackageInfo? _packageInfo;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }

  Future<void> _loadPackageInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _packageInfo = await PackageInfo.fromPlatform();
    } catch (e) {
      print('获取应用信息失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('版本更新'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCurrentVersionInfo(),
                  const SizedBox(height: 24),
                  _buildUpdateSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildCurrentVersionInfo() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '当前版本信息',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('应用名称', _packageInfo?.appName ?? '未知'),
            _buildInfoRow('版本号', _packageInfo?.version ?? '未知'),
            _buildInfoRow('构建号', _packageInfo?.buildNumber ?? '未知'),
            _buildInfoRow('包名', _packageInfo?.packageName ?? '未知'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.blue,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateButton(VersionInfo latestVersion) {
    return ElevatedButton.icon(
      onPressed: () async {
        // 直接使用UpdateService的downloadUpdate方法，并传入版本信息对象
        await _updateService.downloadUpdate(latestVersion.downloadUrl,
            versionInfo: latestVersion);
      },
      icon: const Icon(Icons.system_update),
      label: const Text('立即更新'),
      style: ElevatedButton.styleFrom(
        minimumSize: const Size(double.infinity, 48),
      ),
    );
  }

  Widget _buildUpdateSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '检查更新',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Obx(() {
              final latestVersion = _updateService.latestVersion.value;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (latestVersion != null) ...[
                    _buildInfoRow('最新版本', latestVersion.version),
                    _buildInfoRow('构建号', latestVersion.buildNumber),
                    if (latestVersion.releaseDate.isNotEmpty)
                      _buildInfoRow('发布日期', latestVersion.releaseDate),
                    const SizedBox(height: 16),
                    const Text(
                      '更新内容:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        latestVersion.releaseNotes,
                        style: const TextStyle(height: 1.5),
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (_updateService.updateAvailable.value)
                      _buildUpdateButton(latestVersion),

                    // 显示下载进度
                    if (_updateService.isDownloading.value)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                    '下载进度: ${_updateService.downloadProgress.value}%'),
                                if (Platform.isAndroid)
                                  TextButton(
                                    onPressed: _updateService.cancelDownload,
                                    child: const Text('取消下载'),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            LinearProgressIndicator(
                              value:
                                  _updateService.downloadProgress.value / 100,
                              minHeight: 10,
                              borderRadius: BorderRadius.circular(5),
                            ),
                          ],
                        ),
                      ),
                  ],
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () =>
                              _updateService.checkForUpdates(forceCheck: true),
                          icon: const Icon(Icons.refresh),
                          label: Text(_updateService.isCheckingUpdate.value
                              ? '检查中...'
                              : '检查更新'),
                          style: ElevatedButton.styleFrom(
                            minimumSize: const Size(double.infinity, 48),
                          ),
                        ),
                      ),
                      if (_updateService.updateAvailable.value) ...[
                        const SizedBox(width: 8),
                        TextButton(
                          onPressed: () {
                            _updateService.resetIgnoredVersion();
                            Get.snackbar('提示', '已重置忽略的版本');
                          },
                          child: const Text('重置忽略版本'),
                        ),
                      ],
                    ],
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }
}
