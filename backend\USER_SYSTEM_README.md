# 岱宗文脉用户系统使用指南

本文档提供了岱宗文脉用户注册和登录系统的使用说明。

## 系统概述

岱宗文脉用户系统提供以下功能：
- 用户注册
- 用户登录
- 用户信息管理
- JWT令牌认证
- 用户权限控制

> **注意**：系统使用的数据库名为`novel`，而非`novel_app`

## 后端API接口

### 1. 用户注册

**请求**：
```
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "用户名",
  "email": "用户邮箱",
  "password": "密码"
}
```

**响应**：
```json
{
  "id": 1,
  "username": "用户名",
  "email": "用户邮箱",
  "is_vip": false,
  "is_admin": false,
  "vip_expire_time": null,
  "created_at": "2023-01-01T00:00:00",
  "updated_at": "2023-01-01T00:00:00"
}
```

### 2. 用户登录

**请求**：
```
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "用户名",
  "password": "密码"
}
```

**响应**：
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "id": 1,
    "username": "用户名",
    "email": "用户邮箱",
    "is_vip": false,
    "is_admin": false,
    "vip_expire_time": null,
    "created_at": "2023-01-01T00:00:00",
    "updated_at": "2023-01-01T00:00:00"
  }
}
```

### 3. 获取当前用户信息

**请求**：
```
GET /api/v1/auth/me
Authorization: Bearer {access_token}
```

**响应**：
```json
{
  "id": 1,
  "username": "用户名",
  "email": "用户邮箱",
  "is_vip": false,
  "is_admin": false,
  "vip_expire_time": null,
  "created_at": "2023-01-01T00:00:00",
  "updated_at": "2023-01-01T00:00:00"
}
```

### 4. 更新用户信息

**请求**：
```
PUT /api/v1/auth/me
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "username": "新用户名",  // 可选
  "email": "新邮箱",      // 可选
  "password": "新密码"    // 可选
}
```

**响应**：
```json
{
  "id": 1,
  "username": "新用户名",
  "email": "新邮箱",
  "is_vip": false,
  "is_admin": false,
  "vip_expire_time": null,
  "created_at": "2023-01-01T00:00:00",
  "updated_at": "2023-01-01T00:00:00"
}
```

### 5. 获取所有用户（仅管理员）

**请求**：
```
GET /api/v1/auth/users
Authorization: Bearer {access_token}
```

**响应**：
```json
[
  {
    "id": 1,
    "username": "用户1",
    "email": "用户1邮箱",
    "is_vip": false,
    "is_admin": true,
    "vip_expire_time": null,
    "created_at": "2023-01-01T00:00:00",
    "updated_at": "2023-01-01T00:00:00"
  },
  {
    "id": 2,
    "username": "用户2",
    "email": "用户2邮箱",
    "is_vip": true,
    "is_admin": false,
    "vip_expire_time": "2023-12-31T00:00:00",
    "created_at": "2023-01-01T00:00:00",
    "updated_at": "2023-01-01T00:00:00"
  }
]
```

## 前端使用说明

### 1. 初始化

在应用启动时，AuthController已经被初始化并注册到GetX依赖注入系统中。您可以在任何地方通过以下方式获取AuthController实例：

```dart
final authController = Get.find<AuthController>();
```

### 2. 用户注册

```dart
// 调用注册方法
authController.register(
  username: "用户名",
  email: "用户邮箱",
  password: "密码"
);
```

### 3. 用户登录

```dart
// 调用登录方法
authController.login(
  username: "用户名",
  password: "密码"
);
```

### 4. 检查登录状态

```dart
// 检查用户是否已登录
if (authController.isLoggedIn.value) {
  // 用户已登录
  final user = authController.currentUser.value;
  print("当前用户: ${user.username}");
} else {
  // 用户未登录
  Get.to(() => LoginScreen());
}
```

### 5. 获取用户信息

```dart
// 获取最新的用户信息
await authController.getCurrentUser();
final user = authController.currentUser.value;
```

### 6. 更新用户信息

```dart
// 更新用户信息
await authController.updateUser(
  username: "新用户名",  // 可选
  email: "新邮箱",      // 可选
  password: "新密码"    // 可选
);
```

### 7. 用户登出

```dart
// 用户登出
authController.logout();
```

## 权限控制

系统提供了两种用户角色：
- 普通用户：可以管理自己的小说和个人信息
- 管理员用户：可以查看所有用户信息和管理系统

您可以在前端通过以下方式检查用户权限：

```dart
// 检查用户是否为管理员
if (authController.currentUser.value?.isAdmin == true) {
  // 显示管理员功能
} else {
  // 显示普通用户功能
}

// 检查用户是否为VIP
if (authController.currentUser.value?.isVip == true) {
  // 显示VIP功能
} else {
  // 显示普通功能
}
```

## 安全注意事项

1. 所有API请求都需要在请求头中包含有效的JWT令牌（登录和注册除外）
2. 令牌有效期默认为30分钟，过期后需要重新登录
3. 密码在数据库中使用bcrypt算法加密存储
4. 敏感操作应该验证用户身份和权限
