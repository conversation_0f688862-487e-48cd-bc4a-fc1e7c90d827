# Novel App Windows Build Instructions

This document provides instructions for building the Windows version of Novel App v4.2.5.1.

## Prerequisites

Before building, ensure you have the following installed:

1. **Flutter SDK** - Located at `D:\element\flutter`
2. **Inno Setup 6** - Located at `C:\Program Files (x86)\Inno Setup 6`
3. **Visual Studio 2019 or later** with C++ desktop development workload
4. **VC++ Redistributable Installer** - Should be placed at `D:\project\vs code\novel_app\windows\installer\VC_redist.x64.exe`

## Build Scripts

The following scripts are provided to help with the build process:

### 1. check_environment.bat

This script checks if all required components are installed and properly configured.

```
check_environment.bat
```

### 2. build_windows_4.2.5.1.bat

This script builds the Windows version of Novel App v4.2.5.1 and creates an installer.

```
build_windows_4.2.5.1.bat
```

## Build Process

The build process consists of the following steps:

1. Update version in pubspec.yaml to 4.2.5+1
2. Run flutter clean
3. Get dependencies
4. Build Windows release
5. Create installer directory
6. Build installer with Inno Setup
7. Create version.json for Windows update

## Output Files

After a successful build, you will find the following files:

1. **Windows Installer**: `D:\project\vs code\novel_app\build\windows\installer\DaiZhong_Novel_Setup_4.2.5.1.exe`
2. **Version JSON**: `D:\project\vs code\novel_app\build\windows\installer\version\version.json`

## Deployment

To deploy the update:

1. Upload the installer to your server at: `https://dzwm.xyz/downloads/novel_app_latest.exe`
2. Upload the version.json file to your server at: `https://dzwm.xyz/api/version.json`

## Troubleshooting

If you encounter any issues during the build process:

1. Check that all paths in the scripts match your actual installation paths
2. Ensure you have the necessary permissions to write to the output directories
3. Check that all required components are properly installed
4. Review the error messages for specific issues

## Customization

If you need to customize the build process:

1. Edit `novel_app_setup_4.2.5.1.iss` to modify the installer configuration
2. Edit `build_windows_4.2.5.1.bat` to modify the build process
3. Update paths in both files if your installation locations differ from the defaults
