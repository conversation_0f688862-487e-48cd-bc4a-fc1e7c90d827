<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="测试页面">
  <title>平台检测测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    h1 {
      color: #333;
    }
    .result {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-top: 20px;
    }
    .success {
      color: green;
    }
    .error {
      color: red;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>平台检测测试</h1>
    <p>此页面用于测试平台检测功能是否正常工作。</p>
    
    <div class="result" id="result">
      <p>正在检测平台...</p>
    </div>
    
    <script>
      // 简单的平台检测
      function detectPlatform() {
        const result = document.getElementById('result');
        
        try {
          const userAgent = navigator.userAgent.toLowerCase();
          let platform = 'unknown';
          
          if (userAgent.includes('android')) {
            platform = 'android';
          } else if (userAgent.includes('iphone') || userAgent.includes('ipad') || userAgent.includes('ipod')) {
            platform = 'ios';
          } else if (userAgent.includes('windows')) {
            platform = 'windows';
          } else if (userAgent.includes('macintosh') || userAgent.includes('mac os x')) {
            platform = 'macos';
          } else if (userAgent.includes('linux')) {
            platform = 'linux';
          }
          
          result.innerHTML = `
            <h2 class="success">平台检测成功</h2>
            <p><strong>用户代理:</strong> ${navigator.userAgent}</p>
            <p><strong>检测到的平台:</strong> ${platform}</p>
            <p><strong>浏览器语言:</strong> ${navigator.language}</p>
            <p><strong>处理器核心数:</strong> ${navigator.hardwareConcurrency || '未知'}</p>
          `;
        } catch (error) {
          result.innerHTML = `
            <h2 class="error">平台检测失败</h2>
            <p><strong>错误:</strong> ${error.message}</p>
          `;
        }
      }
      
      // 页面加载完成后执行检测
      window.addEventListener('load', detectPlatform);
    </script>
  </div>
</body>
</html>
